#!/usr/bin/env node

/**
 * Test script to verify the auth flow is working
 */

const axios = require('axios');

async function testAuthFlow() {
    console.log('🔐 Testing Auth Flow');
    console.log('==================\n');

    try {
        // Test 1: Check if server is running
        console.log('1. Testing server connectivity...');
        try {
            const response = await axios.get('http://localhost:8888/users', {
                headers: { 'database': 'database1' }
            });
            console.log('✅ Server is running and responding');
            console.log(`   Users in database: ${response.data.length || 0}`);
        } catch (error) {
            console.log('❌ Server connectivity failed:', error.message);
            console.log('   Make sure the server is running with: node src/index.js');
            return;
        }

        // Test 2: Check auth endpoint
        console.log('\n2. Testing auth endpoint...');
        try {
            const response = await axios.get('http://localhost:8888/auth', {
                maxRedirects: 0,
                validateStatus: (status) => status === 302
            });
            console.log('✅ Auth endpoint is working');
            console.log('   Redirect URL:', response.headers.location);
            
            // Check if redirect URL has correct port
            if (response.headers.location.includes('localhost%3A8888')) {
                console.log('✅ Redirect URI is correctly set to port 8888');
            } else if (response.headers.location.includes('localhost%3A8080')) {
                console.log('❌ Redirect URI is still set to port 8080 - this needs to be fixed');
            }
        } catch (error) {
            console.log('❌ Auth endpoint failed:', error.message);
        }

        // Test 3: Manual user creation test
        console.log('\n3. Testing manual user creation...');
        const testUser = {
            name: 'Test User',
            email: '<EMAIL>',
            password: 'password',
            accessToken: 'test-access-token',
            refreshToken: 'test-refresh-token',
            expiry: Math.floor(Date.now() / 1000) + 3600
        };

        try {
            const response = await axios.post('http://localhost:8888/users', testUser, {
                headers: {
                    'Content-type': 'application/json',
                    'database': 'database1'
                }
            });
            console.log('✅ Manual user creation successful');
            console.log('   Response:', response.data);
        } catch (error) {
            if (error.response?.status === 400 && error.response?.data === 'User Already Exist') {
                console.log('✅ User creation working (user already exists)');
            } else {
                console.log('❌ Manual user creation failed:', error.response?.data || error.message);
            }
        }

        console.log('\n📋 Next Steps:');
        console.log('1. Make sure your Google Cloud Console OAuth redirect URI is set to:');
        console.log('   http://localhost:8888/auth/callback');
        console.log('');
        console.log('2. Test the complete OAuth flow:');
        console.log('   - Visit: http://localhost:8888/auth');
        console.log('   - Complete Google OAuth');
        console.log('   - Check if user is created in database');
        console.log('');
        console.log('3. Check server logs for detailed debugging information');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test if this script is executed directly
if (require.main === module) {
    testAuthFlow();
}

module.exports = {
    testAuthFlow
};
