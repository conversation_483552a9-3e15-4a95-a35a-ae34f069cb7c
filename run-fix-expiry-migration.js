#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to run the expiry column fix migration specifically
 */

const Sequelize = require("sequelize");
const CONFIG = require('./src/config');

async function runExpiryFix() {
    console.log('🔧 Running expiry column fix migration...');
    
    const sequelize = new Sequelize(
        CONFIG.mysql1.database,
        CONFIG.mysql1.username,
        CONFIG.mysql1.password,
        {
            dialect: CONFIG.mysql1.dialect,
            host: CONFIG.mysql1.host,
            logging: console.log
        }
    );

    try {
        // Test connection
        await sequelize.authenticate();
        console.log('✅ Database connection established');

        // Run the ALTER TABLE command directly
        console.log('🔄 Altering expiry column from INTEGER to BIGINT...');
        
        await sequelize.query('ALTER TABLE database1.users MODIFY COLUMN expiry BIGINT');
        
        console.log('✅ Expiry column successfully changed to BIGINT');
        
        // Verify the change
        const [results] = await sequelize.query('DESCRIBE database1.users');
        const expiryColumn = results.find(col => col.Field === 'expiry');
        
        if (expiryColumn) {
            console.log(`✅ Verification: expiry column type is now: ${expiryColumn.Type}`);
        }
        
    } catch (error) {
        console.error('❌ Migration failed:', error.message);
        
        if (error.message.includes('Unknown column')) {
            console.log('ℹ️  The expiry column might not exist yet. Creating it...');
            try {
                await sequelize.query('ALTER TABLE database1.users ADD COLUMN expiry BIGINT');
                console.log('✅ Expiry column created successfully');
            } catch (addError) {
                console.error('❌ Failed to add expiry column:', addError.message);
            }
        }
    } finally {
        await sequelize.close();
        console.log('🔒 Database connection closed');
    }
}

// Run the fix
runExpiryFix().catch(console.error);
