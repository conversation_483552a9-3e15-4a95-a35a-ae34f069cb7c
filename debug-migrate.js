#!/usr/bin/env node

/**
 * Debug migration runner to see what's happening
 */

const Sequelize = require("sequelize");
const { Umzug, SequelizeStorage } = require('umzug');
const CONFIG = require('./src/config');
const path = require('path');
const fs = require('fs');

async function debugMigrations() {
    console.log('🔍 Debug Migration Runner');
    console.log('========================\n');

    // Check if migration files exist
    const migrationDir = path.join(__dirname, 'src/migrations/mysql');
    console.log(`📁 Migration directory: ${migrationDir}`);
    
    if (fs.existsSync(migrationDir)) {
        const files = fs.readdirSync(migrationDir);
        console.log(`📄 Found ${files.length} migration files:`);
        files.forEach(file => console.log(`   - ${file}`));
    } else {
        console.log('❌ Migration directory does not exist!');
        return;
    }

    // Test database connection
    console.log('\n🔌 Testing database connection...');
    const sequelize = new Sequelize(
        CONFIG.mysql1.database,
        CONFIG.mysql1.username,
        CONFIG.mysql1.password,
        {
            dialect: CONFIG.mysql1.dialect,
            host: CONFIG.mysql1.host,
            logging: console.log
        }
    );

    try {
        await sequelize.authenticate();
        console.log('✅ Database connection successful');
    } catch (error) {
        console.log('❌ Database connection failed:', error.message);
        return;
    }

    // Check current migration status
    console.log('\n📊 Checking migration status...');
    const umzug = new Umzug({
        migrations: { 
            glob: "./src/migrations/mysql/*.js",
            resolve: ({ name, path, context }) => {
                console.log(`🔍 Loading migration: ${name} from ${path}`);
                return {
                    name,
                    up: async () => {
                        console.log(`⬆️  Running UP for: ${name}`);
                        const migration = require(path);
                        return migration.up({ context });
                    },
                    down: async () => {
                        console.log(`⬇️  Running DOWN for: ${name}`);
                        const migration = require(path);
                        return migration.down({ context });
                    }
                };
            }
        },
        context: sequelize.getQueryInterface(),
        storage: new SequelizeStorage({ sequelize }),
        logger: console,
    });

    try {
        // Get pending migrations
        const pending = await umzug.pending();
        console.log(`📋 Pending migrations: ${pending.length}`);
        pending.forEach(migration => console.log(`   - ${migration.name}`));

        // Get executed migrations
        const executed = await umzug.executed();
        console.log(`✅ Executed migrations: ${executed.length}`);
        executed.forEach(migration => console.log(`   - ${migration.name}`));

        if (pending.length > 0) {
            console.log('\n🚀 Running pending migrations...');
            await umzug.up();
            console.log('✅ All migrations completed successfully!');
        } else {
            console.log('\n✅ No pending migrations to run');
        }

    } catch (error) {
        console.error('❌ Migration error:', error);
    } finally {
        await sequelize.close();
    }
}

debugMigrations().catch(console.error);
