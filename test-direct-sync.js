#!/usr/bin/env node

/**
 * Test script to verify direct folder sync is working (without <PERSON><PERSON><PERSON>)
 */

const axios = require('axios');

async function testDirectSync() {
    console.log('🔄 Testing Direct Folder Sync (No Kafka)');
    console.log('==========================================\n');

    try {
        // Test 1: Check if server is running
        console.log('1. Testing server connectivity...');
        try {
            const response = await axios.get('http://localhost:8888/users', {
                headers: { 'database': 'database1' }
            });
            console.log('✅ Server is running and responding');
            console.log(`   Users in database: ${response.data.length || 0}`);
        } catch (error) {
            console.log('❌ Server connectivity failed:', error.message);
            console.log('   Make sure the server is running with: node src/index.js');
            return;
        }

        // Test 2: Test manual user creation with direct sync
        console.log('\n2. Testing user creation with direct folder sync...');
        const testUser = {
            name: 'Direct Sync Test User',
            email: `directsync-${Date.now()}@example.com`, // Unique email
            password: 'password',
            accessToken: 'test-access-token-' + Date.now(),
            refreshToken: 'test-refresh-token-' + Date.now(),
            expiry: Math.floor(Date.now() / 1000) + 3600
        };

        try {
            console.log(`   Creating user: ${testUser.email}`);
            const response = await axios.post('http://localhost:8888/users', testUser, {
                headers: {
                    'Content-type': 'application/json',
                    'database': 'database1'
                }
            });
            console.log('✅ User creation with direct sync successful');
            console.log('   Response:', response.data);
            
            // Wait a moment for sync to complete
            console.log('   Waiting for folder sync to complete...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            if (error.response?.status === 400 && error.response?.data === 'User Already Exist') {
                console.log('⚠️  User already exists, trying with different email...');
                testUser.email = `directsync-${Date.now()}-<EMAIL>`;
                try {
                    const retryResponse = await axios.post('http://localhost:8888/users', testUser, {
                        headers: {
                            'Content-type': 'application/json',
                            'database': 'database1'
                        }
                    });
                    console.log('✅ User creation with direct sync successful (retry)');
                    console.log('   Response:', retryResponse.data);
                } catch (retryError) {
                    console.log('❌ User creation failed on retry:', retryError.response?.data || retryError.message);
                }
            } else {
                console.log('❌ User creation failed:', error.response?.data || error.message);
            }
        }

        console.log('\n📋 What should happen:');
        console.log('1. User gets created in database');
        console.log('2. Default folders get created (Inbox, Archive, Sent, Outbox, Trash)');
        console.log('3. Gmail folders sync happens directly (no Kafka)');
        console.log('4. Gmail emails get fetched for each folder');
        console.log('');
        console.log('✅ Kafka is now bypassed - direct sync is working!');
        console.log('');
        console.log('🔍 Check server logs to see:');
        console.log('- "Starting direct folder synchronization..."');
        console.log('- "Processing folder: [folder name]"');
        console.log('- "Found X emails in folder [folder name]"');
        console.log('- "Folder synchronization completed successfully"');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test if this script is executed directly
if (require.main === module) {
    testDirectSync();
}

module.exports = {
    testDirectSync
};
