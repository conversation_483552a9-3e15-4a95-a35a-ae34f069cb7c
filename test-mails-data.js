#!/usr/bin/env node

/**
 * Test script to show what mails data looks like
 */

const getAllFolders = require('./src/external-api-calls');

async function testMailsData() {
    console.log('📧 Testing Mails Data Structure');
    console.log('==============================\n');

    // You'll need to replace this with a real access token from your OAuth flow
    const testAccessToken = 'your-access-token-here';
    const testLabelId = 'INBOX'; // Gmail's inbox label

    try {
        console.log('1. Testing basic email IDs fetch...');
        
        // Current implementation - only gets message IDs
        const basicMails = await getAllFolders.getAllEmails.getAllEmails({
            accessToken: testAccessToken,
            labelId: testLabelId
        });

        console.log('📋 Basic mails data structure:');
        console.log(JSON.stringify(basicMails, null, 2));
        console.log(`\n📊 Found ${basicMails ? basicMails.length : 0} email IDs`);

        console.log('\n2. Testing full email content fetch...');
        
        // Enhanced implementation - gets full email content
        const fullMails = await getAllFolders.getFullEmails.getFullEmails({
            accessToken: testAccessToken,
            labelId: testLabelId,
            maxResults: 3 // Limit to 3 for testing
        });

        console.log('\n📧 Full mails data structure (first email):');
        if (fullMails && fullMails.length > 0) {
            const firstEmail = { ...fullMails[0] };
            delete firstEmail.raw; // Remove raw data for cleaner output
            console.log(JSON.stringify(firstEmail, null, 2));
        }

        console.log(`\n📊 Processed ${fullMails ? fullMails.length : 0} full emails`);

    } catch (error) {
        if (error.message.includes('Invalid Credentials')) {
            console.log('❌ Invalid access token. To test with real data:');
            console.log('1. Complete OAuth flow: http://localhost:8888/auth');
            console.log('2. Check server logs for access token');
            console.log('3. Replace testAccessToken in this script');
        } else {
            console.error('❌ Error:', error.message);
        }
    }

    console.log('\n📋 Current mails data structure:');
    console.log('Basic emails (current): [{ id: "messageId", threadId: "threadId" }]');
    console.log('\nFull emails (enhanced): {');
    console.log('  messageId: "gmail-message-id",');
    console.log('  threadId: "gmail-thread-id",');
    console.log('  subject: "Email subject",');
    console.log('  from: "<EMAIL>",');
    console.log('  to: "<EMAIL>",');
    console.log('  date: "email date",');
    console.log('  body: "email content",');
    console.log('  snippet: "preview text",');
    console.log('  isRead: true/false,');
    console.log('  labelIds: ["INBOX", "IMPORTANT"]');
    console.log('}');
}

// Run the test if this script is executed directly
if (require.main === module) {
    testMailsData();
}

module.exports = {
    testMailsData
};
