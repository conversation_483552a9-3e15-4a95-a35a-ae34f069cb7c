// KAFKA HANDLER COMMENTED OUT - Now using direct calls from user controller
// const { Kafka } = require('kafkajs');
// const usecases = require('../use-cases');
// const getAllFolders = require('../external-api-calls')

// const kafka = new Kafka({
//   clientId: 'my-app',
//   brokers: ['localhost:9092']
// });

// const consumer = kafka.consumer({ groupId: 'my-group' });

// const run = async () => {
//   await consumer.connect();
//   await consumer.subscribe({ topic: 'folders', fromBeginning: false });
//   await consumer.run({
//     eachMessage: async ({ topic, partition, message }) => {
//       // partition,
//       // offset: message.offset,
//       const id = JSON.parse(message.value.toString());
//       // console.log(id.result.userId+"ID GETTING IN CONSUMER ");
//       const accessToken = id.result.accessToken;
//       const result = await getAllFolders.getAllFolders.getAllFolders({ accessToken });
//       for (const obj of result) {
//         console.log(obj.name);
//         const existency = await usecases.folders.folderExistById({ name: obj.name, database: id.result.database, id: id.result.userId })
//         if (existency != true) {
//           // await usecases.folders.createFolder({ id: id.result.userId, name: obj.name, database: id.result.database, providerId: obj.id });
//           const mails = await getAllFolders.getAllEmails.getAllEmails({ accessToken: id.result.accessToken, labelId: obj.id })

//           console.log(mails+"OO");
//         }
//         else {
//           console.log("Folder Already Exist");
//           // await usecases.folders.updateProviderId({ userId: id.result.userId, name: obj.name, database: id.result.database, providerId: obj.id });
//         }
//       }
//     },
//   });
// };

// run().catch(console.error);

console.log("Kafka folder-update handler is disabled. Using direct calls from user controller instead.");