// KAFKA HANDLER COMMENTED OUT - Using direct calls from user controller instead
// const { Kafka } = require('kafkajs');
// const Users = require('../use-cases');
// console.log(Users.users.defaultFolders)
// // const creatingDefaultFolder = require('../use-cases/users/default-folders-add');
// // const defaultFolder = Users.users.defaultFolders({Users,Joi});
// // console.log(defaultFolder);

// const kafka = new Kafka({
//     clientId: 'my-app',
//     brokers: ['localhost:9092']
//   });

// const consumer = kafka.consumer({ groupId: 'my-group' });

// const run = async () => {
//   await consumer.connect();
//   await consumer.subscribe({ topic: 'mytopic' ,fromBeginning: false});

//   await consumer.run({
//     eachMessage: async ({ topic, partition, message }) => {
//         // partition,
//         // offset: message.offset,
//       const id = JSON.parse(message.value.toString());
//       console.log(id.userId);
//       const result = Users.users.defaultFolders({id:id.userId});
//       // console.log(result+"received");
//       // if(result==-1)
//       // {
//       //   console.log("User Created but not folder");
//       // }
//       // else
//       // {
//       //   console.log("sjdgsd");
//       // }
//       // return result;
//       // const defaultFolder= creatingDefaultFolder.users.defaultFolders({,Joi})
//       // Call the usecase or service that creates the default folder for the user
//       // using the information in the message payload
//       // ...
//     },
//   });
// };

// run().catch(console.error);

console.log("Kafka user-edit handler is disabled. Using direct calls from user controller instead.");
