function makeUpdateFolderController({
    Jo<PERSON>,
    updateFolder,
    folderExistById,
}) {
    return async function updateFolderController(req, res) {
        const database = req.headers['database'];
        console.info(`In update folder controller`, req.body);
        const oldName = req.params.name;
        const newName = req.body.name;
        console.log(oldName,newName);
        const id = req.params.id;
        const validateInput = validateUserInput({ Joi });

        try {
            validateInput({newName,oldName,id});
        } catch (error) {
            res.status(400).send(error.message);
            return;
        }
        const ans = await folderExistById({ id,name :newName,database});
        console.log(ans);
        if (ans == true) {
            console.log("EXIST")
            res.status(400).send('Folder Already Exist');
        }
        else {
            console.log("NOT EXIST");
            await updateFolder({ newName:newName, id,oldName :oldName ,database});       
            res.status(200).send("Folder SUCCESSFULLY Updated");
        }
    }
}

function validateUserInput({ Joi }) {
    return function ({newName,oldName,id}) {
        const schema = Joi.object({
            newName: Joi.string().required(),
            oldName:Joi.string().required(),
            id:Joi.number().unsafe().required(),
        });
        
        const { error, value } = schema.validate({newName,oldName,id});
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}

module.exports = Object.freeze({
    makeUpdateFolderController,
});