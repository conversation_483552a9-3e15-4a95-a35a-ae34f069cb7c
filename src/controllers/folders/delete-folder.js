function makedeleteFolderController({
    deleteFolder,
    folderExist,
}) {
    return async function deleteUserController(req, res) {
        const database = req.headers['database'];
        console.info(`In create folder controller`, req.body);
        const id = req.params.id;
        const ans = await folderExist({ id ,database});
        console.log(ans);
        if (ans == true) {
            console.log("Folder Exist")
            const a = await deleteFolder({ id ,database});
            if (a == 1) {
                res.status(200).send("FOLDER DELETED");
            }
            else
            {
                res.status(400).send("Folder not exist");
            }
        }
        else {
            res.send("Folder NOT EXIXT");
        }
    }
}

module.exports = Object.freeze({
    makedeleteFolderController,
});