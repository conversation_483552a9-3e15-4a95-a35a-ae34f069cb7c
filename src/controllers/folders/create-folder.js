function makeCreateFolderController({
    Jo<PERSON>,
    createFolder,
    folderExistById,
}) {
    return async function createFolderController(req, res) {
        const database = req.headers['database'];
        console.info(`In create folder controller`, req.body);
        const name = req.body.name;
        const id = req.params.id;
        const validateInput = validateUserInput({ Joi });
        try {
            validateInput(name);
        } catch (error) {
            res.status(400).send(error.message);
            return;
        }
        console.log("HELLO");
        const ans = await folderExistById({ id,name ,database});
        console.log(ans);
        if (ans == true) {
            console.log("EXIST")
            res.status(400).send('Folder Already Exist');
        }
        else {
            console.log("NOT EXIST");
            await createFolder({ name, id ,database});       
            res.status(200).send("Folder SUCCESSFULLY CREATED");
        }
    }
}

function validateUserInput({ Jo<PERSON> }) {
    return function ok(name) {
        const schema = Joi.object({
            name: Joi.string().required(),
        });

        const { error, value } = schema.validate({  name });
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}


module.exports = Object.freeze({
    makeCreateFolderController,
});