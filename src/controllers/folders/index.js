const usecases = require('../../use-cases')
const gettfolder = require('./get-folder')
const makeDeleteFolder = require('./delete-folder')
const makeCreateFolderController = require('./create-folder');
const makeUpdateFolder = require('./update-folder');
const Joi = require('joi');

const getFolder = gettfolder.getFolder({getAllFolder:usecases.folders.getAllFolder});
const deleteFolder = makeDeleteFolder.makedeleteFolderController({folderExist:usecases.folders.folderExist,deleteFolder:usecases.folders.deleteFolder});
const addFolder = makeCreateFolderController.makeCreateFolderController({createFolder:usecases.folders.createFolder,folderExistById:usecases.folders.folderExistById,Joi})
const updateFolder = makeUpdateFolder.makeUpdateFolderController({updateFolder:usecases.folders.updateFolder,folderExistById:usecases.folders.folderExistById,Joi})
module.exports = Object.freeze({
    getFolder,
    deleteFolder,
    addFolder,
    updateFolder,
});