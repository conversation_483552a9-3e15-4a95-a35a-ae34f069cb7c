module.exports = function makeUpdateUserController({
    Jo<PERSON>,
    getEmailById,
    updateUser,
    userExist,
}) {
    return async function updateUserController(req, res) {
        const database = req.headers['database'];
        console.info(`In update user controller`, req.body);
        const id = req.params.id
        console.log(typeof(id)+"jhskjdhksad");
        const name = req.body.name;
        const validateInput = validateUserInput({ Joi });

        try {
            validateInput(name);
        } catch (error) {
            res.status(400).send(error.message);
            return;
        }
        const email = await getEmailById({ id,database });
        console.log(email+"xjckjc");
        const ans = await userExist({ email,database });
        console.log(ans);
        if (ans == true) {
            console.log("EXIST")
            const a = await updateUser({ email, name ,database});
            if (a<1) {
                res.status(400).send("USER NOT FOUND");
            }
            else {
                res.status(200).send('Updated Successfully');
            }
        }
        else {

        }
    }
}

function validateUserInput({ Joi }) {
    return function (name) {
        const schema = Joi.object({
            name: Joi.string().required(),
        });

        const { error, value } = schema.validate({  name });
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}
