const { getUserController } = require('./user_Controller');
const makeCreateUserController = require('./create-user');
const makedeleteUserController = require('./delete-user');
const makeUpdateUserController = require('./update-user');
const Joi = require('joi');
const usecases = require('../../use-cases');

const createCreateuserController = makeCreateUserController({ Joi, createUser: usecases.users.createUser, userExist: usecases.users.userExist,getIdByEmail:usecases.users.getIdByEmail ,defaultFolders:usecases.users.defaultFolders});

const deleteUser = makedeleteUserController({ userExist: usecases.users.userExist, deleteUser: usecases.users.deleteUser, getEmailById: usecases.users.getEmailById, })

const getAllUser = getUserController({ getUserUseCase: usecases.users.getUserUseCase })

const updateUser = makeUpdateUserController({ getEmailById: usecases.users.getEmailById, userExist: usecases.users.userExist, updateUser: usecases.users.updateUser,Joi })

module.exports = Object.freeze({
    getAllUser,
    createCreateuserController,
    deleteUser,
    updateUser,
});