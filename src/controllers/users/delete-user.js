module.exports = function makedeleteUserController({
    deleteUser,
    userExist,
    getEmailById
}) {
    return async function createUserController(req, res) {
        const database = req.headers['database'];
        const id = req.params.id;
        const email = await getEmailById({ id ,database});
        console.log(database);
        if(email==false)
        {
            res.send("User NOt EXist");
            return;
        }
        const ans = await userExist({ email,database });
        console.log(ans+"Output from usecase");
        if (ans == true) {
            console.log("EXIST")
            await deleteUser({ email ,database});
            res.status(200).send("User Deleted");
        }
        else {
            res.send("USER NOT EXIXT");
        }
    }
}