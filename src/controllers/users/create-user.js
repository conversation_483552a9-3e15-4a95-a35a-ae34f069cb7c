// KAFKA COMMENTED OUT - Using direct calls instead
// const { Kafka } = require('kafkajs')

// // Kafka configuration with fallback options
// const kafkaBrokers = process.env.KAFKA_BROKERS ?
//     process.env.KAFKA_BROKERS.split(',') :
//     ['localhost:9092', '127.0.0.1:9092'];

// const kafka = new Kafka({
//     clientId: 'my-app',
//     brokers: kafkaBrokers,
//     connectionTimeout: 3000,
//     requestTimeout: 25000,
// });

// const producer = kafka.producer({
//     maxInFlightRequests: 1,
//     idempotent: false,
//     transactionTimeout: 30000,
// })

// Import required modules for direct folder sync
const usecases = require('../../use-cases');
const getAllFolders = require('../../external-api-calls');

// Function to sync user folders and emails (extracted from <PERSON>fka handler)
async function syncUserFoldersAndEmails({ userId, accessToken, database }) {
    console.log(`Starting folder sync for user ${userId}`);

    // Get all Gmail folders/labels
    const result = await getAllFolders.getAllFolders.getAllFolders({ accessToken });

    for (const obj of result) {
        console.log(`Processing folder: ${obj.name}`);

        // Check if folder already exists for this user
        const existency = await usecases.folders.folderExistById({
            name: obj.name,
            database: database,
            id: userId
        });

        if (existency != true) {
            console.log(`Creating new folder: ${obj.name}`);
            // Create folder with provider ID
            await usecases.folders.createFolder({
                id: userId,
                name: obj.name,
                database: database,
                providerId: obj.id
            });

            // Get emails for this folder
            const mails = await getAllFolders.getAllEmails.getAllEmails({
                accessToken: accessToken,
                labelId: obj.id
            });

            console.log(`Found ${mails ? mails.length : 0} emails in folder ${obj.name}`);
        } else {
            console.log(`Folder already exists: ${obj.name}`);
            // Update provider ID for existing folder
            await usecases.folders.updateProviderId({
                userId: userId,
                name: obj.name,
                database: database,
                providerId: obj.id
            });
        }
    }
}

module.exports = function makeCreateUserController({
    Joi,
    createUser,
    userExist,
    getIdByEmail,
    defaultFolders,
}) {
    return async function createUserController(req, res) {
        const database = req.headers['database'];
        console.info(`In create user controller`, req.body);
        const email = req.body.email;
        const name = req.body.name;
        const password = req.body.password;
        const accessToken = req.body.accessToken;
        const refreshToken = req.body.refreshToken;
        const expiry = req.body.expiry;
        console.log(expiry+"getting it expiry date in this format");
        const validateInput = validateUserInput({ Joi });

        try {
            validateInput(email, name, password);
        } catch (error) {
            res.status(400).send(error.message);
            return;
        }
        const ans = await userExist({ email,database });
        console.log(ans);
        if (ans == true) {
            console.log("EXIST")
            res.status(400).send('User Already Exist');
        }
        else {
            console.log("NOT EXIST");
            const re = await createUser({ email, name, password ,database,accessToken,refreshToken,expiry});
            console.log(re);
            const id = await getIdByEmail({email,database});
            console.log(id+"ID GETTING IN PRODUCER")
            await defaultFolders({id,database});
            // KAFKA REPLACED WITH DIRECT FOLDER SYNC CALL
            // const data = {
            //     userId:id,
            //     accessToken:accessToken,
            //     database:database
            // }

            // // Send Kafka message with error handling
            // try {
            //     await producer.connect();
            //     console.log("Kafka producer connected successfully");
            //     await producer.send({
            //       topic: 'folders',
            //       messages: [
            //         {
            //           value: JSON.stringify({
            //           result: data,
            //           }),
            //         },
            //       ],
            //     });
            //     console.log("Kafka message sent successfully");
            //     await producer.disconnect();
            // } catch (kafkaError) {
            //     console.error("Kafka error (continuing without Kafka):", kafkaError.message);
            //     // Don't fail user creation if Kafka fails
            // }

            // Direct folder synchronization call
            try {
                console.log("Starting direct folder synchronization...");
                await syncUserFoldersAndEmails({
                    userId: id,
                    accessToken: accessToken,
                    database: database
                });
                console.log("Folder synchronization completed successfully");
            } catch (syncError) {
                console.error("Folder sync error (continuing without sync):", syncError.message);
                // Don't fail user creation if folder sync fails
            }
            res.status(200).send("USER SUCCESSFULLY CREATED");
        }
    }
}

function validateUserInput({ Joi }) {
    return function (email, name, password) {
        const schema = Joi.object({
            email: Joi.string().email().required(),
            name: Joi.string().required(),
            password: Joi.string().required(),
        });

        const { error, value } = schema.validate({ email, name, password});
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}