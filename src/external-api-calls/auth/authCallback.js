// var XMLHttpRequest = require('xmlhttprequest').XMLHttpRequest;

// function makeAuthCallback({
//     client
// }){
//     return async function authCallback(req,res)
//     {
//         const {code} = req.query;
//         try{
//             const {tokens} = await client.getToken(code);
//             console.log(tokens);
//             client.setCredentials(tokens);
//             const data = await client.request({
//                 url:"https://www.googleapis.com/oauth2/v1/userinfo?alt=json",
//                 method:'GET',
//             })

//             console.log("data"+data);
//             const details = {
//                 name :data.name,
//                 email:data.email,
//                 password:"password",
//                 accessToken:tokens.access_token,
//                 refreshToken:tokens.refresh_token,
//                 id:data.id,
//             }
//             const xhr = new XMLHttpRequest()
//                 xhr.open('post','http://localhost:8080/users')
//                 xhr.setRequestHeader('Content-type','application/json');
//                 xhr.send(JSON.stringify(details));
//         }
//         catch(err)
//         {
//             console.log(err);
//         }
//     }
// }

// module.exports = Object.freeze({
//     makeAuthCallback
// })

const axios = require('axios');

function makeAuthCallback({
    client
}) {
    return async function authCallback(req, res) {
        console.info("Inside authcallback function");
        const { code } = req.query;
        try {
            const { tokens } = await client.getToken(code);
            // console.log(tokens);
            client.setCredentials(tokens);
            const { data } = await axios.get('https://www.googleapis.com/oauth2/v1/userinfo?alt=json', {
                headers: {
                    Authorization: `Bearer ${tokens.access_token}`
                }
            });

            // console.log("data", data);
            // console.log("expiry",typeof(tokens.expiry_date));
            const details = {
                name: data.name,
                email: data.email,
                password: "password",
                accessToken: tokens.access_token,
                refreshToken: tokens.refresh_token,
                expiry:tokens.expiry_date,
                id: data.id,
            }
            console.info("calling post user api here");
            await axios.post('http://localhost:8888/users', details, {
                headers: {
                    'Content-type': 'application/json',
                    'database': 'database1'
                }
            });
        }
        catch (err) {
            console.log(err);
        }
    }
}

module.exports = Object.freeze({
    makeAuthCallback
})
