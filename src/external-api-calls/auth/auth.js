function makeAuth({
    client
}){
    return async function authFunction(req,res)
    {
        console.log("Inside auth function")
         const authUrl = client.generateAuthUrl({
            access_type:"offline",
            scope:["email","profile","https://mail.google.com/",
                "https://www.googleapis.com/auth/gmail.modify",
                "https://www.googleapis.com/auth/gmail.readonly",
                "https://www.googleapis.com/auth/gmail.labels",
                "https://www.googleapis.com/auth/gmail.metadata"]
         });
         console.log("authUrl"+authUrl);
         res.redirect(authUrl);
    }
}

module.exports = Object.freeze({
    makeAuth
})
