function gmailFolders({
  axios,
})
{
    return async function getAllFolders({accessToken}) {
        const url = 'https://gmail.googleapis.com/gmail/v1/users/me/labels';
      
        try {
          const response = await axios.get(url, {
            headers: {
              'Authorization': `Bearer ${accessToken}`
            }
          });      
          return response.data.labels;
        } catch (error) {
          console.error(error);
          throw error;
        }
      }
}

module.exports = Object.freeze({
    gmailFolders,
})

