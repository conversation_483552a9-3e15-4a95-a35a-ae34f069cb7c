function gmailFullEmails({
    axios
}) {
    return async function getFullEmails({
        accessToken,
        labelId,
        maxResults = 10
    }) {
        const url = `https://gmail.googleapis.com/gmail/v1/users/me/messages?labelIds=${labelId}&maxResults=${maxResults}`;
        
        try {
            // First, get the list of message IDs
            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                }
            });

            const messages = response.data.messages || [];
            console.log(`Found ${messages.length} email IDs in label ${labelId}`);

            // Then fetch full details for each email
            const fullEmails = [];
            
            for (const message of messages) {
                try {
                    const emailResponse = await axios.get(
                        `https://gmail.googleapis.com/gmail/v1/users/me/messages/${message.id}`,
                        {
                            headers: {
                                'Authorization': `Bearer ${accessToken}`
                            }
                        }
                    );

                    const emailData = emailResponse.data;
                    
                    // Extract useful information
                    const headers = emailData.payload.headers || [];
                    const subject = headers.find(h => h.name === 'Subject')?.value || 'No Subject';
                    const from = headers.find(h => h.name === 'From')?.value || 'Unknown Sender';
                    const to = headers.find(h => h.name === 'To')?.value || '';
                    const date = headers.find(h => h.name === 'Date')?.value || '';
                    
                    // Get email body (simplified - you might need more complex parsing)
                    let body = '';
                    if (emailData.payload.body && emailData.payload.body.data) {
                        body = Buffer.from(emailData.payload.body.data, 'base64').toString();
                    } else if (emailData.payload.parts) {
                        // Handle multipart emails
                        const textPart = emailData.payload.parts.find(part => 
                            part.mimeType === 'text/plain' || part.mimeType === 'text/html'
                        );
                        if (textPart && textPart.body && textPart.body.data) {
                            body = Buffer.from(textPart.body.data, 'base64').toString();
                        }
                    }

                    const processedEmail = {
                        messageId: emailData.id,
                        threadId: emailData.threadId,
                        subject: subject,
                        from: from,
                        to: to,
                        date: date,
                        body: body,
                        snippet: emailData.snippet || '',
                        labelIds: emailData.labelIds || [],
                        isRead: !emailData.labelIds?.includes('UNREAD'),
                        internalDate: emailData.internalDate,
                        raw: emailData // Keep full raw data for debugging
                    };

                    fullEmails.push(processedEmail);
                    console.log(`✅ Processed email: ${subject.substring(0, 50)}...`);
                    
                } catch (emailError) {
                    console.error(`❌ Failed to fetch email ${message.id}:`, emailError.message);
                }
            }

            console.log(`📧 Successfully processed ${fullEmails.length} full emails`);
            return fullEmails;

        } catch (error) {
            console.error('❌ Failed to fetch emails:', error.message);
            throw error;
        }
    }
}

module.exports = Object.freeze({
    gmailFullEmails,
});
