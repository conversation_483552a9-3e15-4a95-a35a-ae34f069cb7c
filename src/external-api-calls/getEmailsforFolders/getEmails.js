function gmailEmails({
    axios
  }) {
    return async function getEmails({
      accessToken,
      labelId
    }) {
      // const url = `https://gmail.googleapis.com/gmail/v1/users/me/messages?labelIds=${labelId}`;
      const url = `https://gmail.googleapis.com/gmail/v1/users/me/messages?labelIds=${labelId}&maxResults=10`;
      try {
        const response = await axios.get(url, {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        });
        console.log(response.data.messages);
        return response.data.messages;
      } catch (error) {
        console.error(error);
        throw error;
      }
    }
  }
  
  module.exports = Object.freeze({
    gmailEmails,
  })