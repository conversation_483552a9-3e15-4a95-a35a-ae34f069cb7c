const CONFIG = require('../../config');
const mysql = require('mysql2');
const users_query = require('./User_query');
const folder_query = require('./folder_query')
const accessTokenUpdate = require('./userAcessTokenUpdate')

const db = mysql.createConnection({
  host: CONFIG.mysql1.host,
  user: CONFIG.mysql1.username,
  password: CONFIG.mysql1.password,
  database: CONFIG.mysql1.database,
})

db.connect(err => {
  if (err) throw err
  console.log('MySQL database connected successfully!')
})

const Users = users_query({db});
const folders = folder_query({db});
const accessToken = accessTokenUpdate({db});
console.log(accessToken);
accessToken();


module.exports = Object.freeze({
  Users,
  folders,
})