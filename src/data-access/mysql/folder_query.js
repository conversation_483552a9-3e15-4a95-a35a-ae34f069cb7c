module.exports  = function FolderDataQuery({db})
{
    return Object.freeze(
        {
            allFolder,
            createfolder,
            folderFolderEXistence,
            updateFolder,
            folderExist,
            deleteFolder,
        }
    )

    async function allFolder({id,database}) {
        try {
            const [rows, fields] = await db.promise().query(`SELECT * FROM ${database}.email_folder where userId = (?) ORDER BY id desc;`,[id]);
            console.log(rows);
            // console.log("IN database ")
            return rows;
        } catch (err) {
            return err;
        }
    }

    async function folderFolderEXistence({name, id,database}) {
        try {
          console.log(id,name);
          const [rows] = await db.promise().query(
            `SELECT COUNT(*) AS folderCount FROM ${database}.email_folder WHERE userId = ? AND name = ?`,
            [id, name]
          );
          console.log(rows);
          const folderCount = rows[0].folderCount;
          console.log(`Folder count: ${folderCount}`);
          return folderCount > 0;
        } catch (err) {
          console.error(`Error checking folder existence: ${err}`);
          throw err;
        }
      };

      async function createfolder({id,name,database}){
       const result =  await db.promise().query(`INSERT INTO ${database}.email_folder (name, userid) VALUES(?,?)`, [name, id]);
       return result[0].affectedRows;
      }

    async function updateFolder({id, newName,oldName,database}) {
        console.log(oldName,newName);
        const result = await db.promise().query(
            `update ${database}.email_folder SET name = ? WHERE userId = ? AND name = ?`, [newName,id,oldName]
        );
        // console.log(result[0].affectedRows)
        return result[0].affectedRows;
    }

    async function folderExist({id,database}) {
        const [rows] = await db.promise().query(
            `SELECT * FROM ${database}.email_folder WHERE id = ?`, [id]
        );
        if (rows.length > 0) {
            return true;
        }
    }

    async function deleteFolder({id,database}) {
        if (id != false) {
            const result = await db.promise().query(
                `delete FROM ${database}.email_folder WHERE id = ?`, [id]
            );
            // console.log(result[0].affectedRows);
            return result[0].affectedRows;
        }
        else {
            return false;
        }
    }
}
