module.exports  = function usersDataQuery({db})
{
    return Object.freeze(
        {
            getUsers,
            createUsers,
            UserExistency,
            getId,
            defaultFoldersAdd,
            deleteUser,
            getEmail,
            updateUser,
        }
    );
     async function getUsers({database}) {
        try {
            const [rows, fields] = await db.promise().query(`SELECT * FROM ${database}.users ORDER BY id desc;`);
            // console.log(rows);
            return rows;
        } catch (err) {
            return err;
        }
    }
    async function createUsers({name,email, password,database}){
        try {
            const [result] = await db.promise().query(
                `INSERT INTO ${database}.users (email, name, password) VALUES(?,?,?)`, [email, name, password]);
            return result.insertId;
        } catch (err) {
            return err;
        }
    }

    async function UserExistency({email,database}) {
        const [rows] = await db.promise().query(
            `SELECT * FROM ${database}.users WHERE email = ?`, [email]
        );
        if (rows.length > 0) {
            return true;
        }
        else
        {
            console.log("IN data check file")
            return false;
        }
    }

    async function getId({email,database}) {
        const [rows] = await db.promise().query(
            `SELECT id FROM ${database}.users WHERE email= ?`, [email]
        );
        if (rows.length > 0) {
            console.log(rows[0].id);
            return rows[0].id;
        }
        else {
            return false;
        }
    }

    async function defaultFoldersAdd({id,database}) {
        const folders = ["Inbox", "Archieve", "Sent", "Outbox", "Trash"];
        let result;
        for (let i in folders) {
             result = await db.promise().query(
                `INSERT INTO ${database}.email_folder (userId,name) VALUES(?,?)`, [id, folders[i]]
            );
        }
        console.log(result[0].affectedRows);
        return result[0].affectedRows;
    }

    async function deleteUser({email,database}) {
        if (email != false) {
            await db.promise().query(
                `delete FROM ${database}.users WHERE email = ?`, [email]
            );
            return true;
        }
        else {
            return false;
        }
    }

    async function getEmail({id,database}) {
        const [rows] = await db.promise().query(
            `SELECT email FROM ${database}.users WHERE id = ?`, [id]
        );
        if (rows.length > 0) {
            console.log(rows[0].id);
            return rows[0].email;
        }
        else {
            return false;
        }
    }

    async function updateUser({email, name,database}) {

        const result = await db.promise().query(
            `update ${database}.users SET name = ? WHERE email = ?`, [name, email]
        );
        return result[0].affectedRows;
    }
}

