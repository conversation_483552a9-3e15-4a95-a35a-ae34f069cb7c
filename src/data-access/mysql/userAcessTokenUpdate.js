const { <PERSON>ronJob } = require('cron');
const refreshAllAccessTokens = require('../../crons')

module.exports = function accessTokenUpdate({ db }) {
  return async function accessToken() {
    try {
      console.log('Starting the job...');
      const job = await new CronJob('* * * * *', () => {
        console.log('Executing the job at:', new Date());
        refreshAllAccessTokens.refreshAllAccessTokens.refreshAllAccessTokens({ db });
      });
      job.start();
      console.log('Job started at:', new Date());
    } catch (err) {
      console.error(err);
      return err;
    }
  };
};
