module.exports = function emailDataQuery({ db }) {
    return Object.freeze({
        createEmail,
        createEmailRecipient,
        emailExists,
        getEmailsByUser,
        getEmailsByFolder
    });

    async function createEmail({
        messageId,
        threadId,
        subject,
        body,
        snippet,
        userId,
        isRead = false,
        inReplyTo = null,
        createdAt,
        database
    }) {
        try {
            const [result] = await db.promise().query(
                `INSERT INTO ${database}.email 
                (messageId, threadId, subject, body, snippet, userId, isRead, inReplyTo, CreatedAt) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [messageId, threadId, subject, body, snippet, userId, isRead, inReplyTo, createdAt]
            );
            return result.insertId;
        } catch (err) {
            console.error('Error creating email:', err);
            throw err;
        }
    }

    async function createEmailRecipient({
        emailId,
        emailAddress,
        type, // 'to', 'cc', 'bcc', 'from'
        database
    }) {
        try {
            const [result] = await db.promise().query(
                `INSERT INTO ${database}.email_recepient (emailId, emailAddress, type) VALUES (?, ?, ?)`,
                [emailId, emailAddress, type]
            );
            return result.insertId;
        } catch (err) {
            console.error('Error creating email recipient:', err);
            throw err;
        }
    }

    async function emailExists({ messageId, database }) {
        try {
            const [rows] = await db.promise().query(
                `SELECT id FROM ${database}.email WHERE messageId = ?`,
                [messageId]
            );
            return rows.length > 0 ? rows[0].id : false;
        } catch (err) {
            console.error('Error checking email existence:', err);
            throw err;
        }
    }

    async function getEmailsByUser({ userId, database, limit = 50 }) {
        try {
            const [rows] = await db.promise().query(
                `SELECT * FROM ${database}.email WHERE userId = ? ORDER BY CreatedAt DESC LIMIT ?`,
                [userId, limit]
            );
            return rows;
        } catch (err) {
            console.error('Error getting emails by user:', err);
            throw err;
        }
    }

    async function getEmailsByFolder({ userId, folderName, database, limit = 50 }) {
        try {
            const [rows] = await db.promise().query(
                `SELECT e.* FROM ${database}.email e
                 JOIN ${database}.email_folder_Association efa ON e.id = efa.email_id
                 JOIN ${database}.email_folder ef ON efa.folderId = ef.id
                 WHERE ef.userId = ? AND ef.name = ?
                 ORDER BY e.CreatedAt DESC LIMIT ?`,
                [userId, folderName, limit]
            );
            return rows;
        } catch (err) {
            console.error('Error getting emails by folder:', err);
            throw err;
        }
    }
};
