const { any } = require("joi");

module.exports  = function FolderDataQuery({db})
{
    return Object.freeze(
        {
            allFolder,
            createfolder,
            folderFolderEXistence,
            updateFolder,
            folderExist,
            deleteFolder,
            updateProviderId,
        }
    )

    async function allFolder({id,database}) {
        try {
            const {rows} = await db.query(`SELECT * FROM ${database}.email_folder where "userId" = ($1) ORDER BY id desc;`,[id]);
            console.log(rows);
            // console.log("IN database ")
            return rows;
        } catch (err) {
            return err;
        }
    }

    async function folderFolderEXistence({name, id,database}) {
        try {
          console.log(id,name);
          const {rows} = await db.query(
            `SELECT COUNT(*) AS folderCount FROM ${database}.email_folder WHERE "userId" = $1 AND name = $2`,
            [id, name]
          );
          console.log(rows);
          const folderCount = rows[0].foldercount;
          console.log(`Folder count: ${folderCount}`);
          return folderCount > 0;
        } catch (err) {
          console.error(`Error checking folder existence: ${err}`);
          throw err;
        }
      };

      async function createfolder({id,name,database,providerId}){
       const result =  await db.query(`INSERT INTO ${database}.email_folder (name, "userId","providerId") VALUES($1,$2,$3)`, [name, id,providerId]);
       return result.rowCount;
      }

    async function updateFolder({id, newName,oldName,database}) {
        console.log(oldName,newName);
        const result = await db.query(
            `update ${database}.email_folders SET name = $1 WHERE "userId" = $2 AND name = $3`, [newName,id,oldName]
        );
        // console.log(result[0].affectedRows)
        return result.rowCount;
    }

    async function folderExist({id,database}) {
        const {rows} = await db.query(
            `SELECT * FROM ${database}.email_folders WHERE id = $1`, [id]
        );
        if (rows.length > 0) {
            return true;
        }
    }

    async function deleteFolder({id,database}) {
        if (id != false) {
            const result = await db.query(
                `delete FROM ${database}.email_folders WHERE id = $1`, [id]
            );
            // console.log(result[0].affectedRows);
            return result.rowCount;
        }
        else {
            return false;
        }
    }
    async function updateProviderId({userId,providerId,database,name})
    {
        console.log("In update provider ID for folder",name);
        const result = await db.query(
            `update ${database}.email_folders SET "providerId" = $1 WHERE "userId" = $2 AND name = $3`, [providerId,userId,name]
        );
        return result;
    }
}
