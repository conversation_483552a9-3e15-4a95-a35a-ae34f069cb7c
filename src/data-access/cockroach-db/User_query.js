module.exports  = function usersDataQuery({db})
{
    return Object.freeze(
        {
            getUsers,
            createUsers,
            UserExistency,
            getId,
            defaultFoldersAdd,
            deleteUser,
            getEmail,
            updateUser,
        }
    );
     async function getUsers({database}) {
        try {
            const {rows} = await db.query(`SELECT * FROM ${database}.users ORDER BY id desc;`);
            console.log(rows);
            return rows;
        } catch (err) {
            return err;
        }
    }
    async function createUsers({name,email, password,database,accessToken,refreshToken,expiry}){
        console.log("yee")
        try {
          const result = await db.query(
            `INSERT INTO ${database}.users (email, name, password,"accessToken","refreshToken",expiry) VALUES($1,$2,$3,$4,$5,$6)`, 
            [email, name, password,accessToken,refreshToken,expiry]
          );
          return result.rowCount;
        } catch (err) {
          return err;
        }
    }

    async function UserExistency({ email, database }) {
        console.log('email:', email);
        const { rows } = await db.query(
          `SELECT * FROM "${database}"."users" WHERE email = $1`,
          [email]
        );
        if (rows && rows.length > 0) {
          return true;
        } else {
          console.log('IN data check file');
          return false;
        }
      }
      

    async function getId({email,database}) {
        const {rows} = await db.query(
            `SELECT id FROM ${database}.users WHERE email= $1`, [email]
        );
        if (rows.length > 0) {
            console.log(rows);
            console.log(rows[0].id);
            return rows[0].id;
        }
        else {
            return false;
        }
    }

    async function getEmail({id, database}) {
        console.log(id);
        const {rows}  = await db.query(
          `SELECT email FROM ${database}.users WHERE id = $1`,
          [id]
        );
        console.log(rows);
        if (rows.length > 0) {
          const email = rows[0].email;
          return email;
        } else {
          return false;
        }
      }
      

    async function defaultFoldersAdd({id,database}) {
        const folders = ["INBOX", "ARCHIEVE", "SENT", "OUTBOX", "TRASH"];
        let result;
        for (let i in folders) {
             result = await db.query(
                `INSERT INTO ${database}.email_folders ("userId",name) VALUES($1,$2)`, [id, folders[i]]
            );
        }
        return result.rowCount;
    }

    async function deleteUser({email,database}) {
        if (email != false) {
            await db.query(
                `delete FROM ${database}.users WHERE email = $1`, [email]
            );
            return true;
        }
        else {
            return false;
        }
    }

    async function updateUser({email, name,database}) {

        const result = await db.query(
            `update ${database}.users SET name = $1 WHERE email = $2`, [name, email]
        );
        return result.rowCount;
    }
}

