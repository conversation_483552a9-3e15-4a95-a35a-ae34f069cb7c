const express = require('express');
const restServiceRouter = require('./rest-service');

const CONFIG = require('./config');

const bodyParser = require('body-parser');
const app = express();

app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false })); 
const port = 8888;
app.use('/', restServiceRouter);

app.listen(port, () => {
  console.log(`Listening at http://${CONFIG.cockroach1.host}:${port}`);
});