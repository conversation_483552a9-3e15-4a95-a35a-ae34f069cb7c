const axios = require('axios');
// const { <PERSON>ronJob } = require('cron');
// const db = require('../data-access'); // assuming you have a module for database access

// function to refresh the access token for a user
async function refreshAccessToken({ user, db }) {
  try {
    const { data: tokens } = await axios.post('https://oauth2.googleapis.com/token', {
      grant_type: 'refresh_token',
      client_id: '288967092794-h0ia1acj2q18usektrk17r0dgj388cs6.apps.googleusercontent.com',
      client_secret: '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',
      refresh_token: user.refreshToken
    });
    const { access_token, expires_in } = tokens;
    console.log(expires_in+"getting it from server in seconds")
    const expiry_date = new Date(Date.now() + expires_in * 1000); // calculate expiry date
    // update the database with the new access token and expiry date
    await db.promise().query('UPDATE database1.users SET accessToken = ?, expiry = ? WHERE id = ?', [access_token, expiry_date, user.id]);
    console.log(`Access token refreshed for user ${user.email}`);
  } catch (error) {
    console.error(`Failed to refresh access token for user ${user.email}:`);
    console.error('Error details:', error.response?.data || error.message);
    console.error('Refresh token:', user.refreshToken ? 'Present' : 'Missing');
  }
}

// function to refresh access tokens for all users
async function refreshAllAccessTokens({ db }) {
  const users = await db.promise().query('SELECT * FROM database1.users');
  for (const user of users[0]) {
    const expiry = user.expiry;
    const date = new Date();
    const timestamp2 = new Date(date).getTime() / 1000;
    console.log(timestamp2);
    if(expiry+1800<=timestamp2)
    refreshAccessToken({ user, db });
  }
}

module.exports = Object.freeze({
  refreshAllAccessTokens
})
