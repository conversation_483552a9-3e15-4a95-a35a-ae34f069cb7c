const fs = require('fs')
const config = {
  mysql1: {
    username: "root",
    password: "admin",
    database: "database1",
    host: "localhost",
    dialect: "mysql",
  },
  mysql2: {
    username: "root",
    password: "admin",
    database: "database2",
    host: "localhost",
    dialect: "mysql",
  },
  mysql3: {
    username: "root",
    password: "admin",
    database: "database3",
    host: "localhost",
    dialect: "mysql",
  },
  mysql4: {
    username: "root",
    password: "admin",
    database: "database4",
    host: "localhost",
    dialect: "mysql",
  },
  mysql5: {
    username: "root",
    password: "admin",
    database: "database5",
    host: "localhost",
    dialect: "mysql",
  },
  cockroach1: {
    user: "root:admin",
    database: "database1",
    host: "localhost",
    port: 26257,
    dialect: "postgresql",
    // dialectOptions: {
    //     ssl: {
    //       rejectUnauthorized: true
    //     }
    //   }
    // ssl: {
    //   ca: fs.readFileSync('/home/<USER>/ritika.jain/Downloads/cockroach/certs/ca.crt').toString(),
    //   cert: fs.readFileSync('/home/<USER>/ritika.jain/Downloads/cockroach/certs/client.root.crt').toString(),
    //   key: fs.readFileSync('/home/<USER>/ritika.jain/Downloads/cockroach/certs/client.root.key').toString(),
    //   rejectUnauthorized: false // This line is added to ignore self-signed certificates
    // }
  },
  cockroach2: {
    user: "root:admin",
    database: "database2",
    host: "localhost",
    port: 26257,
    dialect: "postgresql",
    dialectOptions: {
      ssl: {
        rejectUnauthorized: true
      }
    }
  },
  cockroach3: {
    user: "root:admin",
    database: "database3",
    host: "localhost",
    port: 26257,
    dialect: "postgresql",
    dialectOptions: {
      ssl: {
        rejectUnauthorized: true
      }
    }
  },
  cockroach4: {
    user: "root:admin",
    database: "database4",
    host: "localhost",
    port: 26257,
    dialect: "postgresql",
    dialectOptions: {
      ssl: {
        rejectUnauthorized: true
      }
    }
  },
  cockroach5: {
    user: "root:admin",
    database: "database5",
    host: "localhost",
    port: 26257,
    dialect: "postgresql",
    dialectOptions: {
      ssl: {
        rejectUnauthorized: true
      }
    }
  },

}

module.exports = config;
