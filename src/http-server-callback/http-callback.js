module.exports = function makeHttpCallabck({
    controller, byPassAuthCheck,
    byPassAppCheck, byPassLinkValidation,
  }) {
    return async (req, res) => {
      const httpRequest = {
        body: req.body,
        query: req.query,
        params: req.params,
        ip: req.ip,
        method: req.method,
        path: req.path,
        headers: req.headers,
        app: req.app,
        logger: req.logger,
        uuid: req.uuid,
        linkname: req.headers['x-linkname'],
      };
      
      try {
        const httpResponse = await controller(httpRequest);
        if (httpResponse.headers) {
          for (const header in httpResponse.headers) {
            if (Object.prototype.hasOwnProperty.call(httpResponse.headers,
                header,
            )) {
              res.setHeader(header, httpResponse.headers[header]);
            }
          }
        }
    }
    catch{
        console.log("ERROR");
    }
    }
}