const express = require('express');
const router = express.Router();
const controllers = require('./controllers');
const makeHttpCallback = require('./http-server-callback/http-callback')
const authentication = require('./external-api-calls')

function init()
{
   userRoutes();
   folderRoutes();
   authRoutes();
}

function userRoutes()
{
  router.get('/users', controllers.users.getAllUser);
  // router.get('/users',makeHttpCallback({
  //   controller: controllers.greetAction,
  //   byPassAuthCheck: false,
  //   byPassLinkValidation: false,
  //   byPassAppCheck: false,
  // }))
  
  router.post('/users', controllers.users.createCreateuserController);
  router.delete('/users/:id', controllers.users.deleteUser);
  router.put('/users/:id', controllers.users.updateUser);
}

function folderRoutes()
{
   router.get('/folder/:id',controllers.folders.getFolder);
   router.delete('/folder/:id',controllers.folders.deleteFolder);
   router.post('/folder/:id',controllers.folders.addFolder);
   router.put('/folder/:id/:name',controllers.folders.updateFolder);
}

function authRoutes()
{
   router.get('/auth',authentication.authUser.auth)
   router.get('/auth/callback',authentication.authUser.authCallback)
}

init();

module.exports = router;