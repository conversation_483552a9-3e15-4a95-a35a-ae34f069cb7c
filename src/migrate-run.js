const Sequelize = require("sequelize");
const fs = require('fs');
const { Umzug, SequelizeStorage } = require('umzug');
const CONFIG = require('./config');

function runMigrations_cockroach(config, dbName) {
  const sequelize = new Sequelize(
    config.database,
    config.user.split(':')[0], 
    config.user.split(':')[1], 
    {
      dialect: 'postgres',
      host: config.host,
      port: config.port,
      logging: false,
      dialectOptions: {
        ssl:
        {
          ca: fs.readFileSync('/home/<USER>/ritika.jain/Downloads/cockroach/certs/ca.crt').toString(),
          cert: fs.readFileSync('/home/<USER>/ritika.jain/Downloads/cockroach/certs/client.root.crt').toString(),
          key: fs.readFileSync('/home/<USER>/ritika.jain/Downloads/cockroach/certs/client.root.key').toString(),
        }
      }
    }
  );
  const umzug = new Umzug({
    migrations: { glob: "./migrations/cockroach/*.js" },
    context: sequelize,
    storage: new SequelizeStorage({ sequelize }),
    logger: console,
  });

  umzug.up().then(() => {
    console.log(`All migrations performed successfully on ${dbName} in cockroach`);
  }).catch((err) => {
    console.log(`Migration error on ${dbName}:`, err);
  });
}

function runMigrations_mysql(config, dbName) {
  const sequelize = new Sequelize(
    config.database,
    config.username,
    config.password,
    {
      dialect: config.dialect
    },
  );

  const umzug = new Umzug({
    migrations: { glob: "./migrations/mysql/*.js" },
    context: sequelize.getQueryInterface(),
    storage: new SequelizeStorage({ sequelize }),
    logger: console,
  });

  umzug.up().then(() => {
    console.log(`All migrations performed successfully on ${dbName} in mysql`);
  }).catch((err) => {
    console.log(`Migration error on ${dbName}:`, err);
  });
}

runMigrations_mysql(CONFIG.mysql1, 'database1');
// runMigrations_mysql(CONFIG.mysql2, 'database2');
// runMigrations_mysql(CONFIG.mysql3, 'database3');
// runMigrations_mysql(CONFIG.mysql4, 'database4');
// runMigrations_mysql(CONFIG.mysql5, 'database5');   

// runMigrations_cockroach(CONFIG.cockroach1, 'database1');
// runMigrations_cockroach(CONFIG.cockroach2, 'database2');
// runMigrations_cockroach(CONFIG.cockroach3, 'database3');
// runMigrations_cockroach(CONFIG.cockroach4, 'database4');
// runMigrations_cockroach(CONFIG.cockroach5, 'database5');