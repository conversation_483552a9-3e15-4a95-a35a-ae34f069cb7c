const Sequelize = require('sequelize');

async function up({ context: sequelize }) {
	const email_folder_Association = await sequelize.define('email_folder_Association', {
    folderId: {
      type:Sequelize.INTEGER,
      primaryKey:true,
      references:{
        model: 'email_folders',
        key: 'id',
      },
      onUpdate:"cascade",
      onDelete:"cascade"
    },
    email_id: {
      type:Sequelize.INTEGER,
      primaryKey:true,
      references:{
        model: 'emails',
        key: 'id',
      },
      onUpdate:"cascade",
      onDelete:"cascade"
  },
  },
  {
    timestamps: false // disable createdAt and updatedAt fields
  });
  await email_folder_Association.sync();
}

async function down({ context: sequelize }) {
  await sequelize.drop({ tableName: 'email_folder_Association', schema: 'public' });
}
module.exports = { up, down };