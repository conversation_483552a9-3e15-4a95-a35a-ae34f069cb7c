const Sequelize = require('sequelize');

async function up({ context: sequelize }) {
    // Alter the expiry column from INTEGER to BIGINT to handle large timestamp values
    await sequelize.query('ALTER TABLE database1.users ALTER COLUMN expiry TYPE BIGINT');
}

async function down({ context: sequelize }) {
    // Revert back to INTEGER (this might cause data loss if values are too large)
    await sequelize.query('ALTER TABLE database1.users ALTER COLUMN expiry TYPE INTEGER');
}

module.exports = { up, down };
