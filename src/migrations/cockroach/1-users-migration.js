const Sequelize = require('sequelize');

async function up({ context: sequelize }) {
  const User = sequelize.define('users', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER
    },
    email: {
      allowNull: false,
      type: Sequelize.STRING
    },
    password: {
      allowNull: false,
      type: Sequelize.STRING
    },
    name: {
      allowNull: false,
      type: Sequelize.STRING
    },
    accessToken:{
      type:Sequelize.STRING
    },
    refreshToken:{
      type:Sequelize.STRING
    },
    expiry:{
      type:Sequelize.STRING
    }
  }, {
    timestamps: false // disable createdAt and updatedAt fields
  });

  await User.sync();
}

async function down({ context: sequelize }) {
  await sequelize.drop({ tableName: 'users', schema: 'public' });
}

module.exports = { up, down };
