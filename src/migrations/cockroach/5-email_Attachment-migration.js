const Sequelize = require('sequelize');

async function up({ context: sequelize}) {
	const email_attach = await sequelize.define('email_Attachments', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER
    },
    fileName: {
      type: Sequelize.STRING
    },
    email_id: {
      type:Sequelize.INTEGER,
      references:{
        model: 'emails',
        key: 'id',
      },
      onUpdate:"cascade",
      onDelete:"cascade"
  },
    size: {
      type: Sequelize.INTEGER
    },
    type:{
      type:Sequelize.STRING
    },
    path:{
      type:Sequelize.STRING
    },
  },
  {
    timestamps: false // disable createdAt and updatedAt fields
  });
  await email_attach.sync();
}

async function down({ context: sequelize }) {
  await sequelize.drop({ tableName: 'email_Attachments', schema: 'public' });
}
module.exports = { up, down };
