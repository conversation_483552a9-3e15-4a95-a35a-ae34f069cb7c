const Sequelize = require('sequelize');

async function up({ context: sequelize }) {
	const email_recipient =await sequelize.define('email_recepient', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER
    },
    emailAddress: {
      allowNull: false,
      type: Sequelize.STRING
    },
    emailId: {
      type:Sequelize.INTEGER,
      references:{
        model: 'emails',
        key: 'id',
      },
      onUpdate:"cascade",
      onDelete:"cascade"
  },
    type: {
      allowNull: false,
      // type: 'email_type'
      type: Sequelize.STRING
    },
  },
  {
    timestamps: false // disable createdAt and updatedAt fields
  });
  await email_recipient.sync();
}

async function down({ context: sequelize }) {
  await sequelize.drop({ tableName: 'email_recepient', schema: 'public' });
}
module.exports = { up, down };
