const Sequelize = require('sequelize');

async function up({ context: sequelize}) {
  const email = await sequelize.define('emails', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER
    },
    body: {
      // type: 'body_type' // use STRING instead of ENUM     
      type: Sequelize.STRING
    },
    subject: {
      type: Sequelize.STRING
    },
    threadId: {
      allowNull: false,
      type: Sequelize.INTEGER
    },
    CreatedAt: {
      allowNull: false,
      type: Sequelize.DATE
    },
    userId: {
      type: Sequelize.INTEGER,
      references: {
        model: 'users',
        key: 'id',
      } ,
      onUpdate: "cascade",
      onDelete: "cascade"
    },
    isRead: {
      type: Sequelize.BOOLEAN,
      defaultValue: false
    },
    messageId: {
      allowNull: false,
      type: Sequelize.INTEGER
    },
    inReplyTo: {
      type: Sequelize.STRING
    },
    sheduleadAt: {
      type: Sequelize.TIME
    },
    snippet: {
      type: Sequelize.TEXT
    },
    isArchieve: {
      type: Sequelize.BOOLEAN,
      defaultValue: false
    },
  },
  {
    timestamps: false // disable createdAt and updatedAt fields
  });

  await email.sync();
}

async function down({ context: sequelize }) {
  await sequelize.drop({ tableName: 'email', schema: 'public' });
}

module.exports = { up, down };
