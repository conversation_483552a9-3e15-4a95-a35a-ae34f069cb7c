const Sequelize = require('sequelize');

async function up({ context: sequelize }) {
	const email_folder = await sequelize.define('email_folders', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER
    },
    providerId: {
      type: Sequelize.STRING
    },
    userId: {
      type:Sequelize.INTEGER,
      references:{
        model: 'users',
        key: 'id',
      },
      onUpdate:"cascade",
      onDelete:"cascade"
  },
    name: {
      allowNull: false,
      type: Sequelize.STRING
    },
  },
  {
    timestamps: false // disable createdAt and updatedAt fields
  });
  await email_folder.sync();

}

async function down({ context: sequelize }) {
  await sequelize.drop({ tableName: 'email_folders', schema: 'public' });
}
module.exports = { up, down };