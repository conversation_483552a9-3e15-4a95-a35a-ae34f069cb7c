const Sequelize = require('sequelize');

async function up({ context: queryInterface }) {
	await queryInterface.createTable('email_folder_Association', {
    folderId: {
      type:Sequelize.INTEGER,
      primaryKey:true,
      references:{
        model: 'email_folder',
        key: 'id',
      },
      onUpdate:"cascade",
      onDelete:"cascade"
    },
    email_id: {
      type:Sequelize.INTEGER,
      primaryKey:true,
      references:{
        model: 'email',
        key: 'id',
      },
      onUpdate:"cascade",
      onDelete:"cascade"
  },
  });
}

async function down({ context: queryInterface }) {
	await queryInterface.dropTable('email_folder_Association');
}
module.exports = { up, down };