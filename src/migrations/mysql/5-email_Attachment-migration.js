const Sequelize = require('sequelize');

async function up({ context: queryInterface }) {
	await queryInterface.createTable('email_Attachment', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER
    },
    fileName: {
      type: Sequelize.STRING
    },
    email_id: {
      type:Sequelize.INTEGER,
      references:{
        model: 'email',
        key: 'id',
      },
      onUpdate:"cascade",
      onDelete:"cascade"
  },
    size: {
      type: Sequelize.INTEGER
    },
    type:{
      type:Sequelize.STRING
    },
    path:{
      type:Sequelize.STRING
    },
  });
}

async function down({ context: queryInterface }) {
	await queryInterface.dropTable('email_Attachment');
}
module.exports = { up, down };
