const Sequelize = require('sequelize');

async function up({ context: queryInterface }) {
    // Alter the expiry column from INTEGER to BIGINT to handle large timestamp values
    await queryInterface.changeColumn('users', 'expiry', {
        type: Sequelize.BIGINT,
        allowNull: true
    });
}

async function down({ context: queryInterface }) {
    // Revert back to INTEGER (this might cause data loss if values are too large)
    await queryInterface.changeColumn('users', 'expiry', {
        type: Sequelize.INTEGER,
        allowNull: true
    });
}

module.exports = { up, down };
