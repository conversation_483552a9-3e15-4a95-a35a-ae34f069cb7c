const Sequelize = require('sequelize');

async function up({ context: queryInterface }) {
	await queryInterface.createTable('users', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER
    },
    email: {
      allowNull: false,
      type: Sequelize.STRING
    },
    password: {
      allowNull: false,
      type: Sequelize.STRING
    },
    name: {
      allowNull: false,
      type: Sequelize.STRING
    },
    accessToken:{
      type:Sequelize.STRING
    },
    refreshToken:{
      type:Sequelize.STRING
    },
    expiry:{
      type:Sequelize.BIGINT
    },
  });
}

async function down({ context: queryInterface }) {
	await queryInterface.dropTable('users');
}
module.exports = { up, down };

