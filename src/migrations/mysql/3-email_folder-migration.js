const Sequelize = require('sequelize');

async function up({ context: queryInterface }) {
	await queryInterface.createTable('email_folder', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER
    },
    providerId: {
      type: Sequelize.STRING
    },
    userId: {
      type:Sequelize.INTEGER,
      references:{
        model: 'users',
        key: 'id',
      },
      onUpdate:"cascade",
      onDelete:"cascade"
  },
    name: {
      allowNull: false,
      type: Sequelize.STRING
    },
  });
}

async function down({ context: queryInterface }) {
	await queryInterface.dropTable('email_folder');
}
module.exports = { up, down };