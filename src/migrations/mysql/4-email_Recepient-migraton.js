const Sequelize = require('sequelize');

async function up({ context: queryInterface }) {
	await queryInterface.createTable('email_recepient', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER
    },
    emailAddress: {
      allowNull: false,
      type: Sequelize.STRING
    },
    emailId: {
      type:Sequelize.INTEGER,
      references:{
        model: 'email',
        key: 'id',
      },
      onUpdate:"cascade",
      onDelete:"cascade"
  },
    type: {
      allowNull: false,
      type: Sequelize.ENUM('to', 'cc','bcc','from')
    },
  });
}

async function down({ context: queryInterface }) {
	await queryInterface.dropTable('email_Recepient');
}
module.exports = { up, down };
