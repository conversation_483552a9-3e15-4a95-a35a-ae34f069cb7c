const Sequelize = require('sequelize');

async function up({ context: queryInterface }) {
	await queryInterface.createTable('email', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER
    },
    body: {
      type: Sequelize.ENUM('html', 'text')
    },
    subject: {
      type: Sequelize.STRING
    },
    threadId: {
      allowNull: false,
      type: Sequelize.INTEGER
    },
    CreatedAt: {
      allowNull: false,
      type: Sequelize.DATE
    },
    userId: {
        type:Sequelize.INTEGER,
        references:{
          model: 'users',
          key: 'id',
        } ,
        onUpdate:"cascade",
        onDelete:"cascade"
    },
    isRead:{
      type:Sequelize.BOOLEAN,
      defaultValue:false
    },
    messageId:{
      allowNull:false,
      type:Sequelize.INTEGER
    },
    inReplyTo:{
      type:Sequelize.STRING
    },
    sheduleadAt:{
      type:Sequelize.TIME
    },
    snippet:{
      type:Sequelize.TEXT
    },
    isArchieve:{
      type:Sequelize.BOOLEAN,
      defaultValue:false
    },
  });
}

async function down({ context: queryInterface }) {
	await queryInterface.dropTable('email');
}
module.exports = { up, down };

