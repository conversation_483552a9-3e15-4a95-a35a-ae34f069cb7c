Feature: Assigning default folder to user

    Scenario Outline: providing invalid id then it will give error.
        Given the users id is given for defaultfolder "<id>"
        When I try to create default folders
        Then It will throw  error while creating default folder in it: "<error>" with message: "<message>"
        And createDefaultfolder function will call <getDefaultFolderFunctionCallCount> time

        Examples:

            | id | getDefaultFolderFunctionCallCount | error | message                 |
            |    | 0                                 | Error | '"id" is required'      |
            | hh | 0                                 | Error | '"id" must be a number' |

    Scenario Outline: providing invalid id then it will give error.
        Given the users id is given for defaultfolder "<id>"
        When I try to create default folders
        Then It will now show userFolderEmail: <UserFolderDetail>
        And createDefaultfolder function will call <getDefaultFolderFunctionCallCount> time

        Examples:
            | id | UserFolderDetail | getDefaultFolderFunctionCallCount |
            | 5  | '{"id":1}'       | 1                             |