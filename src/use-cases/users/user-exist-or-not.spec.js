const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const makeUserExistOrNot = require('./user-already-exist');
const sandbox = sinon.createSandbox();

const Users = {
  UserExistency: () => {
  },
};

const ExistUserStub = sandbox.stub(Users, "UserExistency");
ExistUserStub.callsFake((args) => {
  expect(args).deep.equal({
    email: this.email,
  });
  return {Id: 1 };
});

After(() => {
  this.email = undefined;
  this.result = undefined;
  this.error = undefined;

  sandbox.resetHistory();
});

Given("the user's email is {string}",
  (email) => {
    this.email = email || undefined;
    // console.log(email+"bhjaxhjasxjhsx");
  },
);

When('I check if the user exists or not', async () =>{
  const UserExistency = makeUserExistOrNot({
    <PERSON><PERSON>,
    <PERSON>,
  });

  try {
    this.result = await UserExistency(
      {
        email:this.email
      }
    );
  } catch (e) {
    // console.log(e);
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw error: {string} with message: "{string}"', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

Then('then It will show user exist: "{string}"', (ExistDetails) => {
  // console.log(JSON.parse(this.result));
  // console.log(this.result+"nsan")
  // console.log(ExistDetails+"jssm")
  expect(this.result).deep.equal(JSON.parse(ExistDetails));
});

Then("existingUsers function will call {int} time",
  (ExistUserFunctionCallCount) => {
    sinon.assert.callCount(ExistUserStub, ExistUserFunctionCallCount);
  },
);