module.exports = function makeDefaultFolders({
    Users,
    Joi
})
{
    return async function defaultFolders({id,database}){
        console.log(id+"mcnm");
        const validateInput = validateUserInput({ Joi });
    
        try {
            validateInput({id});
        } catch (error) {
            throw error
        }
    
        const result = await Users.defaultFoldersAdd({id,database});
        console.log(result+"hnnnnnn");
        return result;
    }
    
    }

function validateUserInput({ Joi }) {
    return function ({id}) {
        const schema = Joi.object({
            id: Joi.number().integer().min(0).unsafe().required()
        });
        
        const { error, value } = schema.validate({ id });
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}