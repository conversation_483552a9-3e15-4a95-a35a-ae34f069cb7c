module.exports = function makeUserExistOrNot({
    Users,
    Joi
}) {
    return async function UserExistOrNot({ email ,database}) {
        // console.info(`user exist or not`, email);
        const validateInput = validateUserInput({ Joi });

        try {
            validateInput({email});
        } catch (error) {
            throw error
        }
        // console.log(email)
        const ans = await Users.UserExistency({email,database});
        // console.log(ans+"IN user - exist or not file ");
        return ans;
    }
}

function validateUserInput({ Joi }) {
    return function ({email}) {
        const schema = Joi.object({
            email: Joi.string().email().required(),
        })
        const { error, value } = schema.validate({ email });
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}