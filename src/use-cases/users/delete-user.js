module.exports = function makeDeleteUserUseCase({
    Users,
    Joi,
}) {
    return async function deleteUserUsecase({ email,database }) {
        console.log(database+"NB");
        const validateInput = validateUserInput({ Joi });

        try {
            validateInput(email);
        } catch (error) {
            throw error
        }

        // console.info(`Inside delete user use case`, email);
        const result = await Users.deleteUser({email,database});
        // console.log(result);
        // console.log(email);
        return email;
    }
}

function validateUserInput({ Joi }) {
    return function (email) {
        const schema = Joi.object({
            email: Joi.string().email().required(),
        });

        const { error, value } = schema.validate({ email });
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}