const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const makeUpdateUserUseCase = require('./update-user');
const sandbox = sinon.createSandbox();

const Users = {
    updateUser: () => {
  },
};

const updateUserStub = sandbox.stub(Users, "updateUser");
updateUserStub.callsFake((args) => {
  expect(args).deep.equal({
    name: this.name,
    email: this.email,
  });
  return { affectedRow: 1 };
});

After(() => {
  this.name = undefined;
  this.email = undefined;
  this.result = undefined;
  this.error = undefined;

  sandbox.resetHistory();
});

Given("User details given name: {string} and email: {string} to update user",
  (name, email) => {
    this.email = email || undefined;
    this.name = name || undefined;
  },
);

When('Try to update user', async () =>{
  const updateUser = makeUpdateUserUseCase({
    Jo<PERSON>,
    Users,
  });

  try {
    this.result = await updateUser({
      email: this.email,
      name: this.name,
    });
  } catch (e) {
    console.log(e);
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw specific error: {string} with message: {string} while updating user', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

Then('Then It will update user with details: {string}', (updateUserDetails) => {
  // console.log(JSON.parse(this.result));
  // console.log("updateUserDetails" + this.result)
  expect(this.result).deep.equal(JSON.parse(updateUserDetails));
});

Then("updateUser function will call {int} time while updating user",
  (updateUserFunctionCallCount) => {
    sinon.assert.callCount(updateUserStub, updateUserFunctionCallCount);
  },
);