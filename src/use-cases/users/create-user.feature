Feature: Create New User.

      Scenario Outline: Try to create new user with invalid details, then it will throw error.
            Given User details name: "<name>", email: "<email>", and password: "<password>" to create new user
            When Try to create new user
            Then It will throw error: "<error>" with message: "<message>" while creating new user
            And createUsers function will call <createUserFunctionCallCount> time while creating new user

            Examples:
                  | name        | email               | password | createUserFunctionCallCount | error | message                         |
                  |             |                     |          | 0                           | Error | '"name" is required'            |
                  | <PERSON><PERSON><PERSON> Jain |                     |          | 0                           | Error | '"email" is required'           |
                  | R<PERSON><PERSON> Jain | Ritika              |          | 0                           | Error | '"email" must be a valid email' |
                  | R<PERSON><PERSON> Jain | <EMAIL> |          | 0                           | Error | '"password" is required'        |


      Scenario Outline: Try to create new user with valid inputs, then it will new create User.
            Given User details name: "<name>", email: "<email>", and password: "<password>" to create new user
            When Try to create new user
            Then It will create new user with details: <newUserDetails>
            And createUsers function will call <createUserFunctionCallCount> time while creating new user

            Examples:
                  | name        | email                   | password  | newUserDetails | createUserFunctionCallCount |
                  | <PERSON><PERSON><PERSON> Jain | <EMAIL> | bbbvbvbhh | '{"insertId": 1}'    | 1                           |


