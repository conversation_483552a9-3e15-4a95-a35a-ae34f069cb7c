const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const makeDefaultFolders = require('./default-folders-add');
const sandbox = sinon.createSandbox();
const Users = {
    defaultFoldersAdd: () => {
    },
};

const getDefaultFolderUserStub = sandbox.stub(Users, "defaultFoldersAdd");
getDefaultFolderUserStub.callsFake((args) => {
    // console.log(this.id+"j"+args);
  expect(args.id).deep.equal(this.id);
  // console.log(this.id+"cfvhbnm");
  return {id:1};
});

After(() => {
  this.id = undefined;
  this.result = undefined;
  this.error = undefined;
  sandbox.resetHistory();
});

Given('the users id is given for defaultfolder {string}',
  (id) => {
    this.id = id || undefined;
  },
);


When('I try to create default folders', async () =>{
  const defaultFoldersAdd = makeDefaultFolders({
    Joi,
    Users,
  });

  try {
    // console.log(this.id)
    this.result = await defaultFoldersAdd({
      id: this.id,
    });
  } catch (e) {
    // console.log(e);
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw  error while creating default folder in it: {string} with message: "{string}"', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

Then('It will now show userFolderEmail: {string}', (UserFolderDetail) => {
  // console.log(UserEmail);
  // console.log(this.result)
  expect(this.result).deep.equal(JSON.parse(UserFolderDetail));
});

Then("createDefaultfolder function will call {int} time",
  (getDefaultFolderFunctionCallCount) => {
    sinon.assert.callCount(getDefaultFolderUserStub,getDefaultFolderFunctionCallCount);
  },
);