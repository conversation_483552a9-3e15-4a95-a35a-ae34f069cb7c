const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const makeGetIdByEmail = require('./get-id-by-email');
const sandbox = sinon.createSandbox();

const Users = {
  getId: () => {},
};

const getIdUserStub = sandbox.stub(Users, "getId");
getIdUserStub.callsFake((args) => {
  expect(args).deep.equal(this.email);
  return { Id: 1 };
});

After(() => {
  this.email = undefined;
  this.result = undefined;
  this.error = undefined;
  sandbox.resetHistory();
});

Given("the user's email is given {string}",
  (email) => {
    this.email = email || undefined;
  },
);

When('I try to get user id', async () =>{
  const getId = makeGetIdByEmail({
    <PERSON><PERSON>,
    <PERSON>,
  });

  try {
    this.result = await getId({
      email: this.email,
    });
  } catch (e) {
    // console.log(e);
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw some error: {string} with message: "{string}"', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

Then('then It will show userId: "{string}"', (UserId) => {
  // console.log(JSON.parse(this.result));
  // console.log(this.result+"nsan")
  // console.log(UserId+"jssm")
  expect(this.result).deep.equal(JSON.parse(UserId));
});

Then("getIdUsers function will call {int} time",
  (getIdUserFunctionCallCount) => {
    sinon.assert.callCount(getIdUserStub, getIdUserFunctionCallCount);
  },
);