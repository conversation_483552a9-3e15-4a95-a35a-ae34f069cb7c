Feature: User Existence

    Scenario Outline: getting user email by id
        Given the users id is given "<id>"
        When I try to get user email
        Then It will throw some error in it: "<error>" with message: "<message>"
        And getEmailUsers function will call <getEmailUserFunctionCallCount> time

        Examples:

            | id | getEmailUserFunctionCallCount | error | message                 |
            |    | 0                             | Error | '"id" is required'      |
            | hh | 0                             | Error | '"id" must be a number' |

    Scenario Outline: getting user email by id
        Given the users id is given "<id>"
        When I try to get user email
        Then It will now show userEmail: "<UserEmail>"
        And getEmailUsers function will call <getEmailUserFunctionCallCount> time
        
        Examples:
            | id | UserEmail            | getEmailUserFunctionCallCount |
            | 5  | <EMAIL> | 1                             |