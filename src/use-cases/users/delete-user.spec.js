const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const makeDeleteUser = require('./delete-user');
const sandbox = sinon.createSandbox();

const Users = {
  deleteUser: () => {
  },
};

const deleteUserStub = sandbox.stub(Users, "deleteUser");
deleteUserStub.callsFake((args) => {
  expect(args).deep.equal(
    this.email);
  return { email:"<EMAIL>" };
});

After(() => {
  this.email = undefined;
  this.result = undefined;
  this.error = undefined;

  sandbox.resetHistory();
});

Given("User details email: {string} to delete user",
  (email) => {
    this.email = email || undefined;
  },
);

When('Try to Delete user', async () =>{
  const deleteUser = makeDeleteUser({
    <PERSON><PERSON>,
    Users,
  });

  try {
    this.result = await deleteUser({email:this.email});
  } catch (e) {
    // console.log(e);
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw error: {string} with message: "{string}" while deleting user', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

Then('It will delete user with details: {string}', (UserDetails) => {
  // console.log(this.result);
  // console.log(UserDetails);
  expect(this.result).deep.equal(UserDetails);
});


Then("deleteUser function will call {int} time while creating new user",
  (deleteUserFunctionCallCount) => {
    sinon.assert.callCount(deleteUserStub, deleteUserFunctionCallCount);
  },
);