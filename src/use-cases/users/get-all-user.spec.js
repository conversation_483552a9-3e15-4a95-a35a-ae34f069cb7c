const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;

const { allUser } = require('./get_All_User');
const sandbox = sinon.createSandbox();

const Users = {
    getUsers: () => { },
};

const getAllUserStub = sandbox.stub(Users, "getUsers");
getAllUserStub.callsFake(() => {
    // expect(args).deep.equal(this.email);
    return [{ id: 104, email: '<EMAIL>', password: 'undefined', name: 'rimmo', accessToken: null, refreshToken: null}]
});

After(() => {
    this.result = undefined;
    this.error = undefined;
    sandbox.resetHistory();
});

// Given("the user's email is given {string}",
//   (email) => {
//     this.email = email || undefined;
//   },
// );

When('I try to get All Users', async () => {
    const getUsers = allUser({
        Users,
    });

      try {
        this.result = await getUsers({
        });
      } catch (e) {
        console.log(e);
        this.error = {
          name: e.name,
          message: e.message,
        };
      }
});

Then('It will give all Users: {string}', (AllUserdeatils) => {
    console.log(JSON.parse(this.result));
    console.log(this.result+"nsan")
    console.log(UserId+"jssm")
    expect(this.result).deep.equal(JSON.parse(AllUserdeatils));
});

Then("gettingAllUser function will call {int} time while getting User",
    (getAllUserFunctionCallCount) => {
        sinon.assert.callCount(getAllUserStub, getAllUserFunctionCallCount);
    },
);