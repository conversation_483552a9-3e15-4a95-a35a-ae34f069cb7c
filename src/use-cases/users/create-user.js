// const { Kafka } = require('kafkajs')

// const kafka = new Kafka({
//     clientId: 'my-app',
//     brokers: ['localhost:9092']
//   });

//   const producer = kafka.producer()

  module.exports = function makeCreateUserUseCase({
    Joi,
    Users,
}) {
    return async function createUserUsecase({ email, name, password ,database,accessToken,refreshToken,expiry}) {
        // console.info(`Inside create user use case`, email);
        const validateInput = validateUserInput({ Joi });

        try {
            validateInput(name, email, password);
        } catch (error) {
            throw error
        }
        console.log("in use case abhi");
        const result = await Users.createUsers({ name, email, password ,database,accessToken,refreshToken,expiry});
        // await producer.connect();
        // console.log("producer connected successfully");
        // await producer.send({
        //   topic: 'mytopic',
        //   messages: [
        //     {
        //       value: JSON.stringify({
        //       userId: result,
        //       }),
        //     },
        //   ],
        // });
        // console.log("message send successfully");
        // await producer.disconnect();
        return result;
    }
}

function validateUserInput({ Joi }) {
    return function (name, email, password) {
        const schema = Joi.object({
            name: Joi.string().required(),
            email: Joi.string().email().required(),
            password: Joi.string().required(),
        });

        const { error, value } = schema.validate({ email, name, password });
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}