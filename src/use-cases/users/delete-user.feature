Feature: Delete User.

    Scenario Outline: Try to delete user with invalid details, then it will throw error.
        Given User details email: "<email>" to delete user
        When Try to Delete user
        Then It will throw error: "<error>" with message: "<message>" while deleting user
        And deleteUser function will call <deleteUserFunctionCallCount> time while creating new user

        Examples:
            | email  | deleteUserFunctionCallCount | error | message                         |
            |        | 0                           | Error | '"email" is required'           |
            | Ritika | 0                           | Error | '"email" must be a valid email' |


    Scenario Outline: Try to delete user with valid details.
        Given User details email: "<email>" to delete user
        When Try to Delete user
        Then It will delete user with details: <UserDetails>
        And deleteUser function will call <deleteUserFunctionCallCount> time while creating new user

        Examples:
            | email            | deleteUserFunctionCallCount | UserDetails        |
            | <EMAIL> | 1                           | "<EMAIL>" |