const allUser = require('./get_All_User');
const Joi = require('joi')
const makeCreateUserUseCase = require('./create-user');
const data = require('../../data-access');
const makeUserExistOrNot = require('./user-already-exist')
const MakeEmailById = require('./get-email-by-id')
const makeDeleteUserUseCase = require('./delete-user')
const makeUpdateUserUseCase = require('./update-user')
const makeGetIdByEmail = require('./get-id-by-email');
const makeDefaultFolders = require('./default-folders-add')

const getUserUseCase = allUser.allUser({ Users:data.mysql.Users })
const createUser = makeCreateUserUseCase({ Users: data.mysql.Users,Joi });
const userExist = makeUserExistOrNot({ Users: data.mysql.Users, Joi})
const getEmailById = MakeEmailById({ Users: data.mysql.Users,Joi })
const deleteUser = makeDeleteUserUseCase({ Users: data.mysql.Users, Joi})
const updateUser = makeUpdateUserUseCase({ Users: data.mysql.Users, Joi})
const getIdByEmail = makeGetIdByEmail({Users:data.mysql.Users,Joi});
const defaultFolders = makeDefaultFolders({Users:data.mysql.Users,Joi})

module.exports = Object.freeze({
    getUserUseCase,
    createUser,
    userExist,
    getEmailById,
    deleteUser,
    updateUser,
    getIdByEmail,
    defaultFolders,
});
