const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const makeCreateUser = require('./create-user');
const sandbox = sinon.createSandbox();

const Users = {
  createUsers: () => {
  },
};

const createUserStub = sandbox.stub(Users, "createUsers");
createUserStub.callsFake((args) => {
  expect(args).deep.equal({
    name: this.name,
    email: this.email,
    password: this.password
  });
  return { insertId: 1 };
});

After(() => {
  this.name = undefined;
  this.email = undefined;
  this.password = undefined;
  this.result = undefined;
  this.error = undefined;

  sandbox.resetHistory();
});

Given("User details name: {string}, email: {string}, and password: {string} to create new user",
  (name, email, password) => {
    this.email = email || undefined;
    this.name = name || undefined;
    this.password = password || undefined;
  },
);

When('Try to create new user', async () =>{
  const createUsers = makeCreateUser({
    Jo<PERSON>,
    Users,
  });

  try {
    this.result = await createUsers({
      email: this.email,
      name: this.name,
      password: this.password,
    });
  } catch (e) {
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw error: {string} with message: "{string}" while creating new user', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

Then('It will create new user with details: {string}', (newUserDetails) => {
  // console.log(JSON.parse(this.result));
  // console.log("newUserDetails" + this.result)
  expect(this.result).deep.equal(JSON.parse(newUserDetails).insertId);
});

Then("createUsers function will call {int} time while creating new user",
  (createUserFunctionCallCount) => {
    sinon.assert.callCount(createUserStub, createUserFunctionCallCount);
  },
);