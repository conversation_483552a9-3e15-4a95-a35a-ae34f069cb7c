Feature: Getting all Users

    Scenario Outline: Getting all Users
        When I try to get All Users
        Then It will give all Users: <AllUserdeatils>
        And gettingAllUser function will call <getAllUserFunctionCallCount> time while getting User

# Examples:
#     | AllUserdeatils                                                                                                            | getAllUserFunctionCallCount |
#     | "{'value':'[{id: 104,email: `<EMAIL>`,password: `undefined`,name: `rimmo`,accessToken: null,refreshToken: null}]'}" | 1                           |