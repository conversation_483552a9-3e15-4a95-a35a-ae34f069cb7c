Feature: User Existence

    Scenario Outline: getting user id by email
        Given the user's email is given "<email>"
        When I try to get user id
        Then It will throw some error: "<error>" with message: "<message>"
        And getIdUsers function will call <getIdUserFunctionCallCount> time

        Examples:

            | email  | getIdUserFunctionCallCount | error | message                         |
            |        | 0                          | Error | '"email" is required'           |
            | Ritika | 0                          | Error | '"email" must be a valid email' |

    Scenario Outline: getting user id by email
        Given the user's email is given "<email>"
        When I try to get user id
        Then then It will show userId: "<UserId>"
        And getIdUsers function will call <getIdUserFunctionCallCount> time

        Examples:
            | email                | UserId      | getIdUserFunctionCallCount |
            | <EMAIL> | '{"Id": 1}' | 1                          |