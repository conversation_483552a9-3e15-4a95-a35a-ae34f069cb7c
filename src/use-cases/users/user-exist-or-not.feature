Feature: User Existence

    Scenario Outline: Check if user exists or not
        Given the user's email is "<email>"
        When I check if the user exists or not
        Then It will throw error: "<error>" with message: "<message>"
        And existingUsers function will call <ExistUserFunctionCallCount> time

        Examples:

            | email  | ExistUserFunctionCallCount | error | message                         |
            |        | 0                          | Error | '"email" is required'           |
            | Ritika | 0                          | Error | '"email" must be a valid email' |

    Scenario Outline: Check if user exists or not
        Given the user's email is "<email>"
        When I check if the user exists or not
        Then then It will show user exist: "<ExistDetails>"
        And existingUsers function will call <ExistUserFunctionCallCount> time

        Examples:
            | email                | ExistDetails | ExistUserFunctionCallCount |
            | <EMAIL> | '{"Id": 1}'  | 1                          |