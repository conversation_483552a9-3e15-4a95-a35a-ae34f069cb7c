module.exports = function MakeEmailById({
    Users,
    Joi
}) {
    return async function getmail({ id ,database}) {
        // console.info(`id is `, id);
        const validateInput = validateUserInput({ Joi });

        try {
            validateInput({id});
        } catch (error) {
            throw error
        }

        const ans = await Users.getEmail({id,database});
        return ans;
    }
}

function validateUserInput({ Joi }) {
    return function ({id}) {
        const schema = Joi.object({
            id: Joi.number().unsafe().required(),
        });

        const { error, value } = schema.validate({ id });
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}