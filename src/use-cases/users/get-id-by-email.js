module.exports = function makeGetIdByEmail({
    Users,
    Joi
}) {
    return async function getID({ email,database }) {
        // console.info(`email is `, email);
        const validateInput = validateUserInput({ Joi });

        try {
            validateInput(email);
        } catch (error) {
            throw error
        }
        const ans = await Users.getId({email,database});
        console.log(ans+"yes it is insertig id ");
        return ans;
    }
}

function validateUserInput({ Joi }) {
    return function (email) {
        const schema = Joi.object({
            email: Joi.string().email().required(),
        });

        const { error, value } = schema.validate({ email });
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}