const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const MakeEmailById = require('./get-email-by-id');
const sandbox = sinon.createSandbox();
const Users = {
    getEmail: () => {
    },
};

const getEmailUserStub = sandbox.stub(Users, "getEmail");
getEmailUserStub.callsFake((args) => {
  expect(args.id).deep.equal(this.id);
  // console.log(this.id+"cfvhbnm");
  return "<EMAIL>";
});

After(() => {
  this.id = undefined;
  this.result = undefined;
  this.error = undefined;
  sandbox.resetHistory();
});

Given('the users id is given {string}',
  (id) => {
    this.id = id || undefined;
  },
);


When('I try to get user email', async () =>{
  const getEmail = MakeEmailById({
    Jo<PERSON>,
    Users,
  });

  try {
    // console.log(this.id)
    this.result = await getEmail({
      id: this.id,
    });
  } catch (e) {
    // console.log(e);
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw some error in it: {string} with message: "{string}"', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

Then('It will now show userEmail: {string}', (UserEmail) => {
  // console.log(UserEmail);
  // console.log(this.result)
  expect(this.result).deep.equal(UserEmail);
});

Then("getEmailUsers function will call {int} time",
  (getEmailUserFunctionCallCount) => {
    sinon.assert.callCount(getEmailUserStub,getEmailUserFunctionCallCount);
  },
);