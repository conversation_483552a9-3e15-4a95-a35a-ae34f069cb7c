module.exports = function makeUpdateUserUseCase({
    Users,
    Joi
}) {
    return async function updateUserUsecase({ email, name,database }) {
        // console.info(`Inside update user use case`, email);
        const validateInput = validateUserInput({ Joi });
        
        try {
            validateInput({name,email});
        } catch (error) {
            throw error;
        }

        if (email != false) {
            const ans= await Users.updateUser({email, name,database});
            // console.log(ans);
            return ans;
        }
        else {
            return false;
        }
    }
}

function validateUserInput({ Joi }) {
    return function ({name,email}) {
        const schema = Joi.object({
            name: Joi.string().required(),
            email: Joi.string().email().required(),
        });

        const { error, value } = schema.validate({ name,email});
        if (error) {
            // console.log("hjxjax");
            throw new Error(error.details[0].message);
        }
    }
}
