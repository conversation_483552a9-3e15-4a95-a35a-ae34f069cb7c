Feature: update User.

    Scenario Outline: Try to update user with invalid details, then it will throw error.
        Given User details given name: "<name>" and email: "<email>" to update user
        When Try to update user
        Then It will throw specific error: "<error>" with message: <message> while updating user
        And updateUser function will call <updateUserFunctionCallCount> time while updating user

        Examples:
            | name        | email  | updateUserFunctionCallCount | error | message                         |
            |             |        | 0                           | Error | '"name" is required'            |
            | <PERSON><PERSON><PERSON> |        | 0                           | Error | '"email" is required'           |
            | <PERSON><PERSON><PERSON> Jain | Ritika | 0                           | Error | '"email" must be a valid email' |


    Scenario Outline: Try to update user with valid details.
        Given User details given name: "<name>" and email: "<email>" to update user
        When Try to update user
        Then Then It will update user with details: <updateUserDetails>
        And updateUser function will call <updateUserFunctionCallCount> time while updating user


        Examples:
            | name        | email                   | updateUserDetails    | updateUserFunctionCallCount |
            | <PERSON><PERSON><PERSON> | <EMAIL> | '{"affectedRow": 1}' | 1                           |