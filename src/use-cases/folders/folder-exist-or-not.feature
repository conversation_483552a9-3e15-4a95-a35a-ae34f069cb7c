Feature: checking folder exist by Id

    Scenario Outline: Try to check folder exist by id with invalid details, then it will throw error.
        Given folder Eixst details by id: "<id>"
        When Try to check folder exist by Id
        Then It will throw error while checking folder exist by Id : "<error>" with message: <message>
        And FolderExistById function will call <FolderExistByIdFunctionCallCount> time while deleting folder

        Examples:
            | id     | FolderExistByIdFunctionCallCount | error | message                 |
            |        | 0                             | Error | '"id" is required'      |
            | Ritika | 0                             | Error | '"id" must be a number' |

     Scenario Outline: Try to check folder exist by id with invalid details, then it will throw error.
        Given folder Eixst details by id: "<id>"
        When Try to check folder exist by Id
        Then It will show details exist by Id : <FolderDetailsById>
        And FolderExistById function will call <FolderExistByIdFunctionCallCount> time while deleting folder

        Examples:
            | id | FolderExistByIdFunctionCallCount | FolderDetailsById         |
            | 5  | 1                            | '{"affectedRows": 1}' |
