function makeCreateFolderUseCase({
    Joi,
    folders,
}) {
    return async function createFolderUsecase({ id, name,database,providerId}) {
        console.log("DATA"+id,name,database);
        // console.info(`Inside create folder use case`, id,name);
        const validateInput = validateUserInput({ Joi });
        try {
            validateInput({name,id});
        } catch (error) {
            throw error;
        }
        const ans = await folders.createfolder({id, name,database,providerId});
        console.log("Successfully created folder");
        return ans;
    }
}

function validateUserInput({ Joi }) {
    return function ok({name,id}) {
        const schema = Joi.object({
            name: Joi.string().required(),
            id:Joi.number().unsafe().required(),
        });
        const { error, value } = schema.validate({  name ,id});
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}

module.exports = Object.freeze({
    makeCreateFolderUseCase,
});