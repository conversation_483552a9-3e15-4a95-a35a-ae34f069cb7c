Feature: get Folder

    Scenario Outline: Try to get All folder with invalid Id, then it will throw error.
        Given user folder details id: "<id>" to get all folder
        When Try to get all folder for particular User
        Then It will throw error while getting all folder: "<error>" with message: <message>
        And getAllfolders function will call <getALlFolderFunctionCallCount> time while deleting folder

        Examples:
            | id     | getALlFolderFunctionCallCount | error | message                 |
            |        | 0                             | Error | '"id" is required'      |
            | Ritika | 0                             | Error | '"id" must be a number' |

    # Scenario Outline: Try to delete folder with valid details.
    #     Given folder details id: "<id>" to delete folder
    #     When Try to delete folder
    #     Then It will delete folder with details: <FolderDetails>
    #     And deletefolders function will call <deleteFolderFunctionCallCount> time while deleting folder

    #     Examples:
    #         | id | deleteFolderFunctionCallCount | FolderDetails         |
    #         | 1  | 1                             | '{"affectedRows": 1}' |
