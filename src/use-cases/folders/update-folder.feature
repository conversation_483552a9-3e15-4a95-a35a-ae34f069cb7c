Feature: Update Folder

    Scenario Outline: Try to update folder with invalid details, then it will throw error.
        Given folder details oldName: "<oldName>", newName: "<newName>" and id: "<id>" to update folder
        When Try to update folder
        Then It will throw error: "<error>" with message: <message> while updating folder
        And updatefolder function will call <updateFolderFunctionCallCount> time while updating folder

        Examples:
            | oldName | newName | id      | updateFolderFunctionCallCount | error | message                 |
            |         |         |         | 0                             | Error | '"oldName" is required' |
            | Outbox  |         |         | 0                             | Error | '"newName" is required' |
            | Outbox  | Inbox   | newName | 0                             | Error | '"id" must be a number' |
            | Outbox  | Inbox   |         | 0                             | Error | '"id" is required'      |

    Scenario Outline: Try to update folder with valid details.
        Given folder details oldName: "<oldName>", newName: "<newName>" and id: "<id>" to update folder
        When Try to update folder
        Then It will update folder: <updateFolderDetails>
        And updatefolder function will call <updateFolderFunctionCallCount> time while updating folder

        Examples:
            | newName | oldName | id | updateFolderDetails   | updateFolderFunctionCallCount |
            | Draft   | Outbox  | 5  | '{"affectedRows": 1}' | 1                             |


