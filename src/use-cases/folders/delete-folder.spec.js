const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const {makeDeleteFolderUseCase} = require('./delete-folder');
const sandbox = sinon.createSandbox();
const folders = {
    deleteFolder: () => {
  },
};

const deleteFolderStub = sandbox.stub(folders, "deleteFolder");
deleteFolderStub.callsFake((args) => {
  expect(args).deep.equal({
    id: this.id,
  });
  return { affectedRows: 1 };
});

After(() => {
  this.id = undefined;
  this.result = undefined;
  this.error = undefined;

  sandbox.resetHistory();
});

Given("folder details id: {string} to delete folder",
  (id) => {
    this.id = id || undefined;
  },
);

When('Try to delete folder', async () =>{
  const deleteFolder = makeDeleteFolderUseCase({
    Joi,
    folders,
  });

  try {
    this.result = await deleteFolder({
      id: this.id,
    });
  } catch (e) {
    // console.log(e);
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw error while deleting folder: {string} with message: {string} while deleting folder', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

Then('It will delete folder with details: {string}', (FolderDetails) => {
  // console.log(JSON.parse(this.result));
//   console.log(newFolderDetails);
  expect(this.result).deep.equal(JSON.parse(FolderDetails));
});

Then("deletefolders function will call {int} time while deleting folder",
  (deleteFolderFunctionCallCount) => {
    sinon.assert.callCount(deleteFolderStub, deleteFolderFunctionCallCount);
  },
);