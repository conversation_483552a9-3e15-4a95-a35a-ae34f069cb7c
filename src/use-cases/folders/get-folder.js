function makeGetAllFolder({ folders, Joi }) {
  return async function getFolder({ id ,database}) {
    console.log("in get-folder Use case");
    const validateInput = validateUserInput({ Joi });
    try {
      validateInput({ id });
    } catch (error) {
      throw error
    }
    const result = await folders.allFolder({ id,database });
    return result;
  }
}

function validateUserInput({ Joi }) {
  return function ({ id }) {
    const schema = Joi.object({
      id: Joi.number().unsafe().required(),
    });

    const { error, value } = schema.validate({ id });
    if (error) {
      throw new Error(error.details[0].message);
    }
  }
}
module.exports = Object.freeze({
  makeGetAllFolder
});