const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const {makeGetAllFolder} = require('./get-folder');
const sandbox = sinon.createSandbox();
const folders = {
    allFolder: () => {
  },
};

const getAllFolderStub = sandbox.stub(folders, "allFolder");
getAllFolderStub.callsFake((args) => {
  expect(args).deep.equal({
    id: this.id,
  });
  return { affectedRows: 1 };
});

After(() => {
  this.id = undefined;
  this.result = undefined;
  this.error = undefined;

  sandbox.resetHistory();
});

Given("user folder details id: {string} to get all folder",
  (id) => {
    this.id = id || undefined;
  },
);

When('Try to get all folder for particular User', async () =>{
  const allFolder = makeGetAllFolder({
    Joi,
    folders,
  });

  try {
    this.result = await allFolder({
      id: this.id,
    });
  } catch (e) {
    // console.log(e);
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw error while getting all folder: {string} with message: {string}', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

// Then('It will delete folder with details: {string}', (FolderDetails) => {
//   // console.log(JSON.parse(this.result));
// //   console.log(newFolderDetails);
//   expect(this.result).deep.equal(JSON.parse(FolderDetails));
// });

Then("getAllfolders function will call {int} time while deleting folder",
  (getALlFolderFunctionCallCount) => {
    sinon.assert.callCount(getAllFolderStub, getALlFolderFunctionCallCount);
  },
);