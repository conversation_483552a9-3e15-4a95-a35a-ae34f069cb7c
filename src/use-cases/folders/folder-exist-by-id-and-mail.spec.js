const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const {makeFolderExistById} = require('./folder-exist-by-id');
const sandbox = sinon.createSandbox();
const folders = {
    folderFolderEXistence: () => {
  },
};

const existFolderStub = sandbox.stub(folders, "folderFolderEXistence");
existFolderStub.callsFake((args) => {
  expect(args).deep.equal({
    name: this.name,
    id: this.id,
  });
  return { affectedRows: 1 };
});

After(() => {
  this.name = undefined;
  this.id = undefined;
  this.result = undefined;
  this.error = undefined;

  sandbox.resetHistory();
});

Given("folder Exist details name: {string} and id: {string}",
  (name, id) => {
    this.id = id || undefined;
    this.name = name || undefined;
  },
);

When('Try to check folder Exist', async () =>{
  const folderFolderEXistence = makeFolderExistById({
    Joi,
    folders,
  });

  try {
    this.result = await folderFolderEXistence({
      id: this.id,
      name: this.name,
    });
  } catch (e) {
    // console.log(e);
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw error while checking Existing folder: {string} with message: {string} while checking folder', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

Then('It will check Existing folder: {string}', (existFolderDetails) => {
  // console.log(JSON.parse(this.result));
//   console.log(newFolderDetails);
  expect(this.result).deep.equal(JSON.parse(existFolderDetails));
});

Then("existfolders function will call {int} time while checking folder",
  (existFolderFunctionCallCount) => {
    sinon.assert.callCount(existFolderStub, existFolderFunctionCallCount);
  },
);