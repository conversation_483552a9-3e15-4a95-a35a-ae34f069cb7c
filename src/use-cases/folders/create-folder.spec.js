const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const {makeCreateFolderUseCase} = require('./create-folder');
const sandbox = sinon.createSandbox();
const folders = {
    createfolder: () => {
  },
};

const createFolderStub = sandbox.stub(folders, "createfolder");
createFolderStub.callsFake((args) => {
  expect(args).deep.equal({
    name: this.name,
    id: this.id,
  });
  return { affectedRows: 1 };
});

After(() => {
  this.name = undefined;
  this.id = undefined;
  this.result = undefined;
  this.error = undefined;

  sandbox.resetHistory();
});

Given("folder details name: {string} and id: {string} to create new folder",
  (name, id) => {
    this.id = id || undefined;
    this.name = name || undefined;
  },
);

When('Try to create new folder', async () =>{
  const createfolder = makeCreateFolderUseCase({
    Joi,
    folders,
  });

  try {
    this.result = await createfolder({
      id: this.id,
      name: this.name,
    });
  } catch (e) {
    // console.log(e);
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw error: {string} with message: {string} while creating new folder', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

Then('It will create new folder with details: {string}', (newFolderDetails) => {
  // console.log(JSON.parse(this.result));
//   console.log(newFolderDetails);
  expect(this.result).deep.equal(JSON.parse(newFolderDetails));
});

Then("createfolders function will call {int} time while creating new folder",
  (createFolderFunctionCallCount) => {
    sinon.assert.callCount(createFolderStub, createFolderFunctionCallCount);
  },
);