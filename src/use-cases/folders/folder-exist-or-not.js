function makeFolderExistOrNot({
    folders,
    Joi
}) {
    return async function UserExistOrNot({ id,database}) {
        // console.info(`folder exist or not`, id);
        const validateInput = validateUserInput({ Joi });

        try {
            validateInput({ id });
        } catch (error) {
            throw error
        }
        const ans = await folders.folderExist({id,database});
        // console.log(ans);
        return ans;
    }
}

function validateUserInput({ Joi }) {
    return function ({ id }) {
        const schema = Joi.object({
            id: Joi.number().unsafe().required(),
        });

        const { error, value } = schema.validate({ id });
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}

module.exports = Object.freeze({
  makeFolderExistOrNot,
});