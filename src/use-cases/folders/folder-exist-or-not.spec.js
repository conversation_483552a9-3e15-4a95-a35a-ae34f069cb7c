const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const {makeFolderExistOrNot} = require('./folder-exist-or-not');
const sandbox = sinon.createSandbox();
const folders = {
    folderExist: () => {
  },
};

const FolderExistByIdFolderStub = sandbox.stub(folders, "folderExist");
FolderExistByIdFolderStub.callsFake((args) => {
  expect(args).deep.equal({
    id: this.id,
  });
  return { affectedRows: 1 };
});

After(() => {
  this.id = undefined;
  this.result = undefined;
  this.error = undefined;

  sandbox.resetHistory();
});

Given("folder Eixst details by id: {string}",
  (id) => {
    this.id = id || undefined;
  },
);

When('Try to check folder exist by Id', async () =>{
  const folderExist = makeFolderExistOrNot({
    Joi,
    folders,
  });

  try {
    this.result = await folderExist({
      id: this.id,
    });
  } catch (e) {
    // console.log(e);
    this.error = {
      name: e.name,
      message: e.message,
    };
  }
});

Then('It will throw error while checking folder exist by Id : {string} with message: {string}', (error, message) => {
  expect(this.error).deep.equal({
    name: error,
    message,
  });
});

Then('It will show details exist by Id : {string}', (FolderDetailsById) => {
  // console.log(JSON.parse(this.result));
//   console.log(newFolderDetails);
  expect(this.result).deep.equal(JSON.parse(FolderDetailsById));
});

Then("FolderExistById function will call {int} time while deleting folder",
  (FolderExistByIdFunctionCallCount) => {
    sinon.assert.callCount(FolderExistByIdFolderStub, FolderExistByIdFunctionCallCount);
  },
);