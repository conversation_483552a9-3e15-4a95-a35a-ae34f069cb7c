const { Given, When, Then, After } = require('cucumber');
const sinon = require('sinon');
const expect = require('chai').expect;
const Joi = require('joi');

const { makeUpdateFolderUseCase } = require('./update-folder');
const sandbox = sinon.createSandbox();
const folders = {
    updateFolder: () => {
    },
};

const updateFolderStub = sandbox.stub(folders, "updateFolder");
updateFolderStub.callsFake((args) => {
    // expect(args).deep.equal({
    //     id: this.id,
    //     oldName: this.name,
    //     newName: this.newName,
    // });
    return { affectedRows: 1 };
});

After(() => {
    this.oldName = undefined,
    this.newName = undefined,
    this.id = undefined;
    this.result = undefined;
    this.error = undefined;

    sandbox.resetHistory();
});

Given("folder details oldName: {string}, newName: {string} and id: {string} to update folder",
    (oldName,newName, id) => {
        this.id = id || undefined;
        this.oldName = oldName || undefined;
        this.newName = newName || undefined;
    },
);

When('Try to update folder', async () => {
    const updateFolder = makeUpdateFolderUseCase({
        Joi,
        folders,
    });

    try {
        this.result = await updateFolder({
            id: this.id,
            oldName: this.oldName,
            newName:this.newName,
        });
    } catch (e) {
        // console.log(e);
        this.error = {
            name: e.name,
            message: e.message,
        };
    }
});

Then('It will throw error: {string} with message: {string} while updating folder', (error, message) => {
    expect(this.error).deep.equal({
        name: error,
        message,
    });
});

Then('It will update folder: {string}', (updateFolderDetails) => {
    // console.log(this.result);
    // console.log(updateFolderDetails);
    expect(this.result).deep.equal((JSON.parse(updateFolderDetails)));
});

Then("updatefolder function will call {int} time while updating folder",
    (updateFolderFunctionCallCount) => {
        sinon.assert.callCount(updateFolderStub, updateFolderFunctionCallCount);
    },
);