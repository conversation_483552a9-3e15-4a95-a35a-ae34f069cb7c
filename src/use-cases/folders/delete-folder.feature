Feature: Delete Folder

    Scenario Outline: Try to delete folder with invalid details, then it will throw error.
        Given folder details id: "<id>" to delete folder
        When Try to delete folder
        Then It will throw error while deleting folder: "<error>" with message: <message> while deleting folder
        And deletefolders function will call <deleteFolderFunctionCallCount> time while deleting folder

        Examples:
            | id     | deleteFolderFunctionCallCount | error | message                 |
            |        | 0                             | Error | '"id" is required'      |
            | Ritika | 0                             | Error | '"id" must be a number' |

    Scenario Outline: Try to delete folder with valid details.
        Given folder details id: "<id>" to delete folder
        When Try to delete folder
        Then It will delete folder with details: <FolderDetails>
        And deletefolders function will call <deleteFolderFunctionCallCount> time while deleting folder

        Examples:
            | id | deleteFolderFunctionCallCount | FolderDetails         |
            | 1  | 1                             | '{"affectedRows": 1}' |
