Feature: Create New Folder

    Scenario Outline: Try to create new folder with invalid details, then it will throw error.
        Given folder details name: "<name>" and id: "<id>" to create new folder
        When Try to create new folder
        Then It will throw error: "<error>" with message: <message> while creating new folder
        And createfolders function will call <createFolderFunctionCallCount> time while creating new folder

        Examples:
            | name        | id     | createFolderFunctionCallCount | error | message                 |
            |             |        | 0                             | Error | '"name" is required'    |
            | <PERSON><PERSON><PERSON> |        | 0                             | Error | '"id" is required'      |
            | <PERSON><PERSON><PERSON> Jain | Ritika | 0                             | Error | '"id" must be a number' |

    Scenario Outline: Try to create new folder with valid details.
        Given folder details name: "<name>" and id: "<id>" to create new folder
        When Try to create new folder
        Then It will create new folder with details: <newFolderDetails>
        And createfolders function will call <createFolderFunctionCallCount> time while creating new folder

        Examples:
            | name        | id | newFolderDetails      | createFolderFunctionCallCount |
            | <PERSON><PERSON><PERSON> Jain | 1  | '{"affectedRows": 1}' | 1                             |


