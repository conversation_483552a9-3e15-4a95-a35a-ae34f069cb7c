const Joi = require('joi');
const data = require('../../data-access')
const makeGettAllFolder = require('./get-folder')
const makeFolderExist = require('./folder-exist-or-not')
const makeDeleteFolderUseCase = require('./delete-folder')
const makeFolderExistById = require('./folder-exist-by-id')
const makeCreateFolderUseCase = require('./create-folder')
const makeUpdateFolderUseCase = require('./update-folder')
const makeUpdateProviderId = require('./update-provider-id')

const getAllFolder = makeGettAllFolder.makeGetAllFolder({folders :data.cockroach.folders,Joi});
const folderExist = makeFolderExist.makeFolderExistOrNot({folders:data.cockroach.folders,Joi})
const deleteFolder = makeDeleteFolderUseCase.makeDeleteFolderUseCase({folders:data.cockroach.folders,Joi})
const folderExistById = makeFolderExistById.makeFolderExistById({folders:data.cockroach.folders,Joi})
const createFolder = makeCreateFolderUseCase.makeCreateFolderUseCase({folders:data.cockroach.folders,Joi})
const updateFolder = makeUpdateFolderUseCase.makeUpdateFolderUseCase({folders:data.cockroach.folders,Joi})
const updateProviderId = makeUpdateProviderId.makeUpdateProviderId({folders:data.cockroach.folders});
module.exports=Object.freeze({
    getAllFolder,
    folderExist,
    deleteFolder,
    folderExistById,
    createFolder,
    updateFolder,
    updateProviderId
});