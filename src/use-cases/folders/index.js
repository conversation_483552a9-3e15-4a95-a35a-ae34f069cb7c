const Joi = require('joi');
const data = require('../../data-access')
const makeGettAllFolder = require('./get-folder')
const makeFolderExist = require('./folder-exist-or-not')
const makeDeleteFolderUseCase = require('./delete-folder')
const makeFolderExistById = require('./folder-exist-by-id')
const makeCreateFolderUseCase = require('./create-folder')
const makeUpdateFolderUseCase = require('./update-folder')
const makeUpdateProviderId = require('./update-provider-id')

const getAllFolder = makeGettAllFolder.makeGetAllFolder({folders :data.mysql.folders,Joi});
const folderExist = makeFolderExist.makeFolderExistOrNot({folders:data.mysql.folders,Joi})
const deleteFolder = makeDeleteFolderUseCase.makeDeleteFolderUseCase({folders:data.mysql.folders,Joi})
const folderExistById = makeFolderExistById.makeFolderExistById({folders:data.mysql.folders,Joi})
const createFolder = makeCreateFolderUseCase.makeCreateFolderUseCase({folders:data.mysql.folders,Joi})
const updateFolder = makeUpdateFolderUseCase.makeUpdateFolderUseCase({folders:data.mysql.folders,Joi})
const updateProviderId = makeUpdateProviderId.makeUpdateProviderId({folders:data.mysql.folders});
module.exports=Object.freeze({
    getAllFolder,
    folderExist,
    deleteFolder,
    folderExistById,
    createFolder,
    updateFolder,
    updateProviderId
});