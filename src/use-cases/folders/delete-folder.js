function makeDeleteFolderUseCase({
    folders,
    Joi
}) {
    return async function deleteUserUsecase({ id ,database}) {
        // console.info(`Inside delete user use case`, id);
        const validateInput = validateUserInput({ Joi });

        try {
            validateInput({ id });
        } catch (error) {
            throw error
        }
        const a = await folders.deleteFolder({ id ,database});
        return a;
    }
}

function validateUserInput({ Joi }) {
    return function ({ id }) {
        const schema = Joi.object({
            id: Joi.number().unsafe().required(),
        });

        const { error, value } = schema.validate({ id });
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}

module.exports = Object.freeze({
    makeDeleteFolderUseCase
});