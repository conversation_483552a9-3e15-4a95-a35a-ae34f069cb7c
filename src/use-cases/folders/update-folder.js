function makeUpdateFolderUseCase({
    folders,
    Joi
}) {
    return async function updateFolderUsecase({ newName,oldName,id,database}) {
        // console.info(`Inside create folder use case`, id,newName);
        const validateInput = validateUserInput({ Joi });
        
        try {
            validateInput({newName,oldName,id});
        } catch (error) {
            throw error;
        }
        const result = await folders.updateFolder({id, newName,oldName,database});
        // console.log(result+"xsxjasgxsax");
        return result;
    }
}

function validateUserInput({ Joi }) {
    return function ({newName,oldName,id}) {
        const schema = Joi.object({
            oldName:Joi.string().required(),
            newName: Joi.string().required(),
            id:Joi.number().unsafe().required(),
        });
        const { error, value } = schema.validate({ newName,oldName,id });
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}


module.exports = Object.freeze({
    makeUpdateFolderUseCase,
});