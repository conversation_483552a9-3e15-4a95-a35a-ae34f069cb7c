function makeFolderExistById({
    folders,
    Joi
}) {
    return async function folderExistOrNot({ name,id,database}) {
        // console.info(`folder exist or not`, id);
        const validateInput = validateUserInput({ Joi });
        try {
            validateInput({name,id});
        } catch (error) {
            throw error;
        }
        const ans = await folders.folderFolderEXistence({name,id,database});
        // console.log(ans);
        return ans;
    }
}

function validateUserInput({ Joi }) {
    return function ok({name,id}) {
        const schema = Joi.object({
            name: Joi.string().required(),
            id:Joi.number().unsafe().required(),
        });
        const { error, value } = schema.validate({  name ,id});
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}

module.exports = Object.freeze({
  makeFolderExistById,
})