Feature:  checking existing Folder by email and id

    Scenario Outline: Try to checking folder with invalid details, then it will throw error.
        Given folder Exist details name: "<name>" and id: "<id>"
        When Try to check folder Exist
        Then It will throw error while checking Existing folder: "<error>" with message: <message> while checking folder
        And existfolders function will call <existFolderFunctionCallCount> time while checking folder

        Examples:
            | name        | id     | existFolderFunctionCallCount | error | message                 |
            |             |        | 0                             | Error | '"name" is required'    |
            | <PERSON><PERSON><PERSON> |        | 0                             | Error | '"id" is required'      |
            | <PERSON><PERSON><PERSON> | Ritika | 0                             | Error | '"id" must be a number' |

    Scenario Outline: Try to check folder with valid details.
         Given folder Exist details name: "<name>" and id: "<id>"
        When Try to check folder Exist
        Then It will check Existing folder: <existFolderDetails>
        And existfolders function will call <existFolderFunctionCallCount> time while checking folder

        Examples:
            | name        | id | existFolderDetails      | existFolderFunctionCallCount |
            | <PERSON><PERSON><PERSON> Jain | 1  | '{"affectedRows": 1}' | 1                             |


