{"devDependencies": {"sequelize-cli": "^6.6.0"}, "scripts": {"test": "cucumber-js ./use-cases/users/"}, "dependencies": {"axios": "^1.3.5", "body-parser": "^1.20.2", "chai": "^4.3.7", "cron": "^2.3.0", "cucumber": "^6.0.7", "express": "^4.18.2", "google-auth-library": "^8.7.0", "googleapis": "^159.0.0", "joi": "^17.8.3", "kafkajs": "^2.2.4", "moment": "^2.30.1", "mysql": "^2.18.1", "mysql2": "^3.2.0", "nodemon": "^2.0.22", "pg": "^8.10.0", "sequelize": "^6.30.0", "sequelize-cockroachdb": "^6.0.5", "sinon": "^15.0.2", "umzug": "^3.2.1", "xmlhttprequest": "^1.8.0"}}