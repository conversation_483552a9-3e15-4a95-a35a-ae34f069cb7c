#!/usr/bin/env node

/**
 * Test Kafka connectivity
 */

const { Kafka } = require('kafkajs');

async function testKafka() {
    console.log('🔌 Testing Kafka Connection');
    console.log('==========================\n');

    const kafkaBrokers = ['localhost:9092', '127.0.0.1:9092'];
    
    for (const broker of kafkaBrokers) {
        console.log(`Testing broker: ${broker}`);
        
        const kafka = new Kafka({
            clientId: 'test-client',
            brokers: [broker],
            connectionTimeout: 3000,
            requestTimeout: 5000,
        });

        const producer = kafka.producer({
            maxInFlightRequests: 1,
            idempotent: false,
            transactionTimeout: 30000,
        });

        try {
            console.log(`  Connecting to ${broker}...`);
            await producer.connect();
            console.log(`  ✅ Connected to ${broker}`);
            
            console.log(`  Sending test message...`);
            await producer.send({
                topic: 'test-topic',
                messages: [
                    {
                        value: JSON.stringify({
                            test: 'message',
                            timestamp: new Date().toISOString()
                        }),
                    },
                ],
            });
            console.log(`  ✅ Message sent successfully`);
            
            await producer.disconnect();
            console.log(`  ✅ Disconnected from ${broker}\n`);
            
            console.log(`🎉 Kafka is working with broker: ${broker}`);
            return;
            
        } catch (error) {
            console.log(`  ❌ Failed to connect to ${broker}: ${error.message}\n`);
            try {
                await producer.disconnect();
            } catch (disconnectError) {
                // Ignore disconnect errors
            }
        }
    }
    
    console.log('❌ All Kafka brokers failed');
    console.log('\nTroubleshooting:');
    console.log('1. Check if Kafka is running: ss -tlnp | grep 9092');
    console.log('2. Check Docker containers: docker ps | grep kafka');
    console.log('3. Check Kafka logs for errors');
}

if (require.main === module) {
    testKafka().catch(console.error);
}

module.exports = { testKafka };
